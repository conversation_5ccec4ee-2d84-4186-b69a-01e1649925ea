#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Configuration Unifiée - Vérification que toutes les méthodes utilisent la même config
"""

import os
import time

def test_configuration_unifiee():
    """Test que la configuration unifiée est appliquée partout"""
    print("🧪 TEST CONFIGURATION UNIFIÉE")
    print("=" * 60)
    
    # Vérifier le fichier
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé : {dataset_path}")
        return False
    
    print(f"✅ Dataset trouvé : {dataset_path}")
    
    try:
        from analyse_complete_avec_diff import CACHE_RAM_GLOBAL
        print("✅ CACHE_RAM_GLOBAL importé")
        
        # Test de la configuration unifiée
        print("\n🔧 TEST CONFIGURATION UNIFIÉE")
        print("-" * 40)
        
        # Charger avec la configuration unifiée
        print("🔄 Chargement avec configuration unifiée...")
        debut = time.time()
        
        analyseur_entropique, analyseur_ratios, donnees_analyse, donnees_volatilite = CACHE_RAM_GLOBAL.charger_dataset_unique(dataset_path)
        
        fin = time.time()
        temps = fin - debut
        
        print(f"✅ Chargement terminé en {temps:.2f}s")
        
        # Vérifier la configuration des analyseurs
        print("\n📊 VÉRIFICATION CONFIGURATION ANALYSEURS")
        print("-" * 40)
        
        config = CACHE_RAM_GLOBAL.obtenir_configuration_unifiee()
        print(f"🎯 Configuration unifiée :")
        print(f"   💾 Cache principal : {config['cache_principal'] / (1024**3):.0f}GB")
        print(f"   🚀 Buffer JSON parsing : {config['buffer_json_parsing'] / (1024**2):.0f}MB")
        print(f"   📊 Chunks streaming : {config['chunks_streaming'] / (1024**2):.0f}MB")
        print(f"   🔢 Batches calculs : {config['batches_calculs'] / (1024**2):.0f}MB")
        print(f"   🖥️ Multiprocessing : {config['buffer_multiprocessing'] / (1024**2):.0f}MB")
        print(f"   ⚡ Cœurs : {config['nb_coeurs']}")
        print(f"   📦 Chunk size : {config['chunk_size']:,} parties/cœur")
        print(f"   🔄 Mode : {config['mode_analyse']}")
        
        # Vérifier que les analyseurs ont la bonne configuration
        print("\n🔍 VÉRIFICATION ANALYSEURS")
        print("-" * 40)
        
        # Analyseur entropique
        if hasattr(analyseur_entropique, 'max_workers'):
            print(f"✅ Analyseur entropique - Cœurs : {analyseur_entropique.max_workers}")
        if hasattr(analyseur_entropique, 'chunk_size'):
            print(f"✅ Analyseur entropique - Chunk size : {analyseur_entropique.chunk_size:,}")
        if hasattr(analyseur_entropique, 'buffer_size'):
            print(f"✅ Analyseur entropique - Buffer : {analyseur_entropique.buffer_size / (1024**2):.0f}MB")
            
        # Analyseur ratios
        if hasattr(analyseur_ratios, 'max_workers'):
            print(f"✅ Analyseur ratios - Cœurs : {analyseur_ratios.max_workers}")
        if hasattr(analyseur_ratios, 'chunk_size'):
            print(f"✅ Analyseur ratios - Chunk size : {analyseur_ratios.chunk_size:,}")
        if hasattr(analyseur_ratios, 'buffer_size'):
            print(f"✅ Analyseur ratios - Buffer : {analyseur_ratios.buffer_size / (1024**2):.0f}MB")
        
        # Vérifier les données
        print(f"\n📊 DONNÉES CHARGÉES")
        print(f"   📈 Analyses : {len(donnees_analyse):,}")
        print(f"   📊 Volatilités : {len(donnees_volatilite):,}")
        
        # Afficher les statistiques du cache
        print(f"\n💾 STATISTIQUES CACHE")
        CACHE_RAM_GLOBAL.afficher_statistiques_cache()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_configuration_unifiee()
    if success:
        print("\n🎉 TEST RÉUSSI : Configuration unifiée appliquée partout")
        print("✅ Cache 8GB + Buffer 3GB + 8 cœurs + Streaming optimisé")
        print("✅ Toutes les méthodes utilisent la même configuration")
    else:
        print("\n❌ TEST ÉCHOUÉ : problème avec la configuration unifiée")
