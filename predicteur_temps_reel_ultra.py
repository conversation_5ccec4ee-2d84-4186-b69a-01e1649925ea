#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prédicteur Temps Réel Ultra-Sophistiqué
SIMULATION COMPLÈTE MAIN N+1 AVEC TOUTES LES POSSIBILITÉS

Ce prédicteur simule en temps réel toutes les possibilités pour la main n+1 :
- Calcule signatures entropiques L4/L5 pour séquences INDEX5
- Calcule signature entropique globale (main 1 → main n)
- Simule toutes les possibilités INDEX5 pour main n+1
- Calcule RATIO_L4/L5, DIFF, DIFF_L4, DIFF_L5, S/O/E pour chaque simulation
- Prédit S/O selon les conditions du tableau prédictif

Basé sur analyseur_transitions_index5.py et nos analyses complètes.

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
from dataclasses import dataclass
from typing import List, Dict
from datetime import datetime
import math

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class EtatPartieActuel:
    """État actuel d'une partie à la main n"""
    sequence_index5: List[str]  # Séquence INDEX5 complète jusqu'à main n (format: "INDEX1_INDEX2_INDEX3")
    sequence_index1: List[str]  # Séquence INDEX1 complète jusqu'à main n (BANKER/PLAYER/TIE)
    main_actuelle: int          # Numéro de la main actuelle (n)
    ratio_l4_actuel: float      # RATIO_L4 à la main n
    ratio_l5_actuel: float      # RATIO_L5 à la main n
    ratio_l4_precedent: float   # RATIO_L4 à la main n-1
    ratio_l5_precedent: float   # RATIO_L5 à la main n-1

@dataclass
class SimulationMainN1:
    """Simulation d'une possibilité pour la main n+1"""
    index5_simule: str          # Valeur INDEX5 simulée pour main n+1 (format: "INDEX1_INDEX2_INDEX3")
    index1_simule: str          # Valeur INDEX1 correspondante (BANKER/PLAYER/TIE)
    ratio_l4_simule: float      # RATIO_L4 calculé pour main n+1
    ratio_l5_simule: float      # RATIO_L5 calculé pour main n+1
    diff_simule: float          # DIFF = |L4-L5| pour main n+1
    diff_l4_simule: float       # DIFF_L4 = |L4(n+1) - L4(n)|
    diff_l5_simule: float       # DIFF_L5 = |L5(n+1) - L5(n)|
    pattern_soe_simule: str     # Pattern S/O/E simulé pour main n+1

@dataclass
class PredictionTempsReel:
    """Résultat de prédiction temps réel avec logique majoritaire"""
    prediction_finale: str      # 'BANKER', 'PLAYER', 'TIE', 'ABSTENTION', ou 'WAIT'
    probabilite: float          # Probabilité de la prédiction (% de simulations majoritaires)
    confiance: str              # Niveau de confiance ('TRÈS_ÉLEVÉE', 'ÉLEVÉE', 'MODÉRÉE', 'FAIBLE', 'AUCUNE')
    nb_simulations_s: int       # [LEGACY] Conservé pour compatibilité
    nb_simulations_o: int       # [LEGACY] Conservé pour compatibilité
    nb_simulations_total: int   # Nombre total de simulations utilisées
    simulations_detaillees: List[SimulationMainN1]  # Détails des simulations
    conditions_activees: List[str]  # [LEGACY] Conservé pour compatibilité
    justification: str          # Justification de la prédiction majoritaire

class PredicteurTempsReelUltra:
    """
    Prédicteur temps réel ultra-sophistiqué avec simulation complète
    """

    def __init__(self):
        """Initialise le prédicteur avec toutes les bases nécessaires"""
        print("🚀 INITIALISATION PRÉDICTEUR TEMPS RÉEL ULTRA")
        print("=" * 60)

        # Charger les modules nécessaires
        self._charger_modules()

        # Charger les bases de signatures entropiques
        self._charger_bases_signatures()

        # Charger les conditions prédictives du tableau
        self._charger_conditions_predictives()

        # Charger le générateur de transitions BCT
        self._charger_generateur_bct()

        # Règles INDEX5 selon BCT (format: "INDEX1_INDEX2_INDEX3")
        self.index1_values = ['0', '1']
        self.index2_values = ['A', 'B', 'C']
        self.index3_values = ['BANKER', 'PLAYER', 'TIE']

        print("✅ Prédicteur temps réel initialisé")
        print(f"✅ Générateur BCT chargé")
        print(f"✅ Bases signatures L4/L5 chargées")
        print(f"✅ Conditions prédictives chargées")
        print(f"✅ Règles INDEX1/INDEX2 intégrées")

    def _charger_modules(self):
        """Charge les modules nécessaires"""
        try:
            from analyseur_transitions_index5 import AnalyseurEvolutionEntropique
            from module_signatures_entropiques import GenerateurSignaturesEntropiques
            # Modules disponibles
            self.AnalyseurEntropique = AnalyseurEvolutionEntropique
            self.GenerateurSignatures = GenerateurSignaturesEntropiques

            print("✅ Modules chargés avec succès")

        except ImportError as e:
            print(f"❌ Erreur chargement modules: {e}")
            raise

    def _charger_bases_signatures(self):
        """Charge les bases de signatures entropiques L4 et L5"""
        try:
            # Utiliser le générateur de signatures
            generateur = self.GenerateurSignatures()

            # Charger les bases L4 et L5
            print("🔄 Chargement bases signatures L4...")
            self.base_signatures_l4 = generateur.charger_base_signatures_4()

            print("🔄 Chargement bases signatures L5...")
            self.base_signatures_l5 = generateur.charger_base_signatures_5()

            print(f"✅ Base L4: {len(self.base_signatures_l4):,} signatures")
            print(f"✅ Base L5: {len(self.base_signatures_l5):,} signatures")

        except Exception as e:
            print(f"❌ Erreur chargement bases signatures: {e}")
            raise

    def _charger_generateur_bct(self):
        """Charge le générateur de transitions BCT"""
        try:
            # Importer la classe GenerateurSequencesBCT depuis l'analyseur
            from analyseur_transitions_index5 import GenerateurSequencesBCT
            self.generateur_bct = GenerateurSequencesBCT()
            print("✅ Générateur BCT chargé avec succès")
        except ImportError as e:
            print(f"❌ Erreur chargement générateur BCT: {e}")
            raise

    def _charger_conditions_predictives(self):
        """Charge les conditions prédictives du tableau final"""
        # CONDITIONS S (CONTINUATION) - Simplifiées pour performance temps réel
        self.conditions_s = [
            # CONDITIONS EXCEPTIONNELLES
            {
                'nom': 'INCOH_TRÈS_INCOHÉRENT',
                'test': lambda d: abs(d['ratio_l4'] - d['ratio_l5']) > 0.5,
                'probabilite': 98.4
            },
            {
                'nom': 'DIFF_SIGNAL_INUTILISABLE',
                'test': lambda d: d['diff'] > 0.300,
                'probabilite': 78.2
            },
            {
                'nom': 'COMPLEX_INCOH_FORTE_VAR',
                'test': lambda d: abs(d['ratio_l4'] - d['ratio_l5']) > 0.2 and (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1),
                'probabilite': 75.9
            },
            {
                'nom': 'COMB_VARIATIONS_FORTES_DIFF_DOUTEUX',
                'test': lambda d: (d['diff_l4'] > 0.1 or d['diff_l5'] > 0.1) and d['diff'] > 0.150,
                'probabilite': 72.1
            },
            {
                'nom': 'INCOH_INCOHÉRENT',
                'test': lambda d: 0.2 <= abs(d['ratio_l4'] - d['ratio_l5']) < 0.5,
                'probabilite': 72.1
            },
            {
                'nom': 'DIFF_SIGNAL_TRÈS_DOUTEUX',
                'test': lambda d: 0.200 <= d['diff'] < 0.300,
                'probabilite': 71.6
            },
            {
                'nom': 'COMB_ORDRE_FORT_DIFF_DOUTEUX',
                'test': lambda d: d['ratio_l4'] < 0.5 and d['diff'] > 0.150,
                'probabilite': 70.5
            },
            # CONDITIONS FORTES
            {
                'nom': 'L5_ORDRE_TRÈS_FORT',
                'test': lambda d: d['ratio_l5'] < 0.3,
                'probabilite': 68.8
            },
            {
                'nom': 'L4_ORDRE_TRÈS_FORT',
                'test': lambda d: d['ratio_l4'] < 0.3,
                'probabilite': 68.5
            },
            {
                'nom': 'COMB_ORDRE_FORT_DIFF_EXCELLENT',
                'test': lambda d: d['ratio_l4'] < 0.5 and 0.020 <= d['diff'] < 0.030,
                'probabilite': 68.0
            },
            {
                'nom': 'DIFF_L5_TRÈS_FORTE_VAR',
                'test': lambda d: 0.2 <= d['diff_l5'] < 0.5,
                'probabilite': 64.8
            },
            {
                'nom': 'DIFF_L4_TRÈS_FORTE_VAR',
                'test': lambda d: 0.2 <= d['diff_l4'] < 0.5,
                'probabilite': 64.1
            },
            {
                'nom': 'DIFF_SIGNAL_DOUTEUX',
                'test': lambda d: 0.150 <= d['diff'] < 0.200,
                'probabilite': 64.1
            }
        ]

        # CONDITIONS O (ALTERNANCE)
        self.conditions_o = [
            {
                'nom': 'COMPLEX_CHAOS_VARIABLE',
                'test': lambda d: d['ratio_l4'] > 1.0 and d['diff_l4'] > 0.1,
                'probabilite': 56.2
            },
            {
                'nom': 'DIFF_SIGNAL_ACCEPTABLE',
                'test': lambda d: 0.075 <= d['diff'] < 0.100,
                'probabilite': 55.9
            },
            {
                'nom': 'INCOH_MODÉRÉMENT_COHÉRENT',
                'test': lambda d: 0.05 <= abs(d['ratio_l4'] - d['ratio_l5']) < 0.1,
                'probabilite': 55.7
            },
            {
                'nom': 'DIFF_L4_FAIBLE_VAR',
                'test': lambda d: 0.02 <= d['diff_l4'] < 0.05,
                'probabilite': 55.5
            },
            {
                'nom': 'COMB_CHAOS_DIFF_DOUTEUX',
                'test': lambda d: d['ratio_l4'] > 0.9 and d['diff'] > 0.150,
                'probabilite': 55.4
            },
            {
                'nom': 'COMPLEX_CHAOS_STABLE',
                'test': lambda d: d['ratio_l4'] > 1.0 and d['diff_l4'] < 0.02,
                'probabilite': 55.3
            },
            {
                'nom': 'COMB_CHAOS_CHAOS',
                'test': lambda d: d['ratio_l4'] > 0.9 and d['ratio_l5'] > 0.9,
                'probabilite': 55.2
            },
            {
                'nom': 'DIFF_L4_STABLE',
                'test': lambda d: 0.01 <= d['diff_l4'] < 0.02,
                'probabilite': 55.2
            }
        ]

        print(f"✅ {len(self.conditions_s)} conditions S chargées")
        print(f"✅ {len(self.conditions_o)} conditions O chargées")

    def _extraire_index3_depuis_index5(self, index5_combined: str) -> str:
        """Extrait INDEX3 (BANKER/PLAYER/TIE) depuis INDEX5"""
        if not index5_combined or not isinstance(index5_combined, str):
            return "UNKNOWN"

        parties = index5_combined.split('_')
        if len(parties) >= 3:
            return parties[2]  # INDEX3 = BANKER/PLAYER/TIE
        return "UNKNOWN"

    def _generer_index5_valides_pour_main_n1(self, etat_partie: EtatPartieActuel) -> List[str]:
        """
        Génère SEULEMENT les INDEX5 valides BANKER/PLAYER pour la main n+1 (TIE exclus)

        Args:
            etat_partie: État actuel de la partie

        Returns:
            List[str]: Liste des INDEX5 valides BANKER/PLAYER uniquement
        """
        if not etat_partie.sequence_index5:
            # Si aucune séquence, générer toutes les possibilités initiales SANS TIE
            index5_valides = []
            for index1 in self.index1_values:
                for index2 in self.index2_values:
                    for index3 in ['BANKER', 'PLAYER']:  # EXCLUSION DES TIE DÈS LA GÉNÉRATION
                        index5_valides.append(f"{index1}_{index2}_{index3}")
            return index5_valides

        # Obtenir le dernier INDEX5 de la séquence
        dernier_index5 = etat_partie.sequence_index5[-1]

        # Utiliser le générateur BCT pour obtenir les transitions valides
        transitions_valides_brutes = self.generateur_bct.generer_transition_valide(dernier_index5)

        # FILTRER pour exclure tous les INDEX5 contenant TIE
        transitions_valides_sans_tie = []
        for index5 in transitions_valides_brutes:
            if not index5.endswith('_TIE'):  # Exclure tous les INDEX5 se terminant par TIE
                transitions_valides_sans_tie.append(index5)

        return transitions_valides_sans_tie

    def predire_main_suivante(self, etat_partie: EtatPartieActuel) -> PredictionTempsReel:
        """
        Prédit la main n+1 en simulant toutes les possibilités INDEX5

        Args:
            etat_partie: État actuel de la partie à la main n

        Returns:
            PredictionTempsReel: Prédiction complète avec simulations
        """
        print(f"🎯 PRÉDICTION MAIN {etat_partie.main_actuelle + 1}")
        print("=" * 50)

        # 1. Calculer l'entropie globale actuelle (main 1 → main n)
        entropie_globale_actuelle = self._calculer_entropie_globale(
            etat_partie.sequence_index5
        )

        print(f"📊 Entropie globale actuelle: {entropie_globale_actuelle:.6f}")

        # 2. Générer SEULEMENT les INDEX5 valides selon les règles BCT
        index5_valides = self._generer_index5_valides_pour_main_n1(etat_partie)
        print(f"🎯 {len(index5_valides)} INDEX5 valides générés selon les règles BCT")

        # 3. Simuler toutes les possibilités INDEX5 valides pour main n+1
        simulations_brutes = []
        simulations_conformes = []

        for index5_possible in index5_valides:
            simulation = self._simuler_main_n1(
                etat_partie,
                index5_possible
            )
            simulations_brutes.append(simulation)

            # CONTRAINTE DÉCROISSANCE L5 : Exclure si ratio L5 > ratio L5 actuel
            if simulation.ratio_l5_simule <= etat_partie.ratio_l5_actuel:
                simulations_conformes.append(simulation)

        print(f"✅ {len(simulations_brutes)} simulations brutes générées")
        print(f"🎯 {len(simulations_conformes)} simulations conformes à la décroissance L5")
        print(f"❌ {len(simulations_brutes) - len(simulations_conformes)} simulations exclues (L5 croissant)")

        # Analyser l'impact de la contrainte décroissance L5
        analyse_decroissance = self._analyser_impact_decroissance_l5(
            simulations_brutes, simulations_conformes, etat_partie.ratio_l5_actuel
        )
        print(f"📊 Exclusion: {analyse_decroissance['pourcentage_exclusion']:.1f}% des simulations")
        print(f"📊 Ratio L5 actuel: {analyse_decroissance['ratio_l5_actuel']:.6f}")
        if analyse_decroissance['nb_simulations_conformes'] > 0:
            print(f"📊 Ratio L5 max conforme: {analyse_decroissance['ratio_l5_max_conforme']:.6f}")
        if analyse_decroissance['nb_simulations_exclues'] > 0:
            print(f"📊 Ratio L5 min exclu: {analyse_decroissance['ratio_l5_min_exclu']:.6f}")

        # Utiliser seulement les simulations conformes à la décroissance
        simulations = simulations_conformes

        # Vérifier qu'il reste des simulations après filtrage décroissance L5
        if not simulations:
            print("⚠️ AUCUNE SIMULATION CONFORME À LA DÉCROISSANCE L5")
            return self._fallback_vers_l4(etat_partie, simulations_brutes)

        # 4. Extraire les INDEX1 (résultats) des simulations conformes
        # Plus besoin de séparer car aucun TIE généré !
        resultats_index1 = []

        for simulation in simulations:
            index1 = self._extraire_index3_depuis_index5(simulation.index5_simule)
            resultats_index1.append(index1)

        # 5. Compter les résultats BANKER/PLAYER (plus de TIE possible)
        comptage_vote = {
            'BANKER': resultats_index1.count('BANKER'),
            'PLAYER': resultats_index1.count('PLAYER')
        }

        print(f"📊 Comptage VOTE MAJORITAIRE (TIE exclus dès la génération):")
        print(f"   BANKER: {comptage_vote['BANKER']}/{len(simulations)}")
        print(f"   PLAYER: {comptage_vote['PLAYER']}/{len(simulations)}")
        print(f"   TIE: 0 (exclus dès la génération des INDEX5)")

        # 6. Déterminer la prédiction finale selon la majorité (BANKER vs PLAYER uniquement)
        prediction_finale = self._determiner_prediction_majoritaire_simplifie(
            comptage_vote, simulations, etat_partie, simulations_brutes, "L5"
        )

        print(f"🎯 PRÉDICTION FINALE: {prediction_finale.prediction_finale}")
        print(f"📈 PROBABILITÉ: {prediction_finale.probabilite:.1f}%")
        print(f"🔒 CONFIANCE: {prediction_finale.confiance}")

        return prediction_finale

    def _analyser_impact_decroissance_l5(self, simulations_brutes: List[SimulationMainN1],
                                       simulations_conformes: List[SimulationMainN1],
                                       ratio_l5_actuel: float) -> Dict:
        """
        Analyse l'impact de la contrainte décroissance L5 sur les simulations

        Args:
            simulations_brutes: Toutes les simulations générées
            simulations_conformes: Simulations respectant la décroissance L5
            ratio_l5_actuel: Ratio L5 de la main actuelle

        Returns:
            Dict: Statistiques sur l'impact de la contrainte
        """
        nb_brutes = len(simulations_brutes)
        nb_conformes = len(simulations_conformes)
        nb_exclues = nb_brutes - nb_conformes

        # Analyser les simulations exclues
        simulations_exclues = [s for s in simulations_brutes if s.ratio_l5_simule > ratio_l5_actuel]

        # Statistiques des ratios L5
        ratios_l5_bruts = [s.ratio_l5_simule for s in simulations_brutes]
        ratios_l5_conformes = [s.ratio_l5_simule for s in simulations_conformes]
        ratios_l5_exclus = [s.ratio_l5_simule for s in simulations_exclues]

        analyse = {
            'nb_simulations_brutes': nb_brutes,
            'nb_simulations_conformes': nb_conformes,
            'nb_simulations_exclues': nb_exclues,
            'pourcentage_exclusion': (nb_exclues / nb_brutes * 100) if nb_brutes > 0 else 0,
            'ratio_l5_actuel': ratio_l5_actuel,
            'ratio_l5_min_brut': min(ratios_l5_bruts) if ratios_l5_bruts else 0,
            'ratio_l5_max_brut': max(ratios_l5_bruts) if ratios_l5_bruts else 0,
            'ratio_l5_min_conforme': min(ratios_l5_conformes) if ratios_l5_conformes else 0,
            'ratio_l5_max_conforme': max(ratios_l5_conformes) if ratios_l5_conformes else 0,
            'ratio_l5_min_exclu': min(ratios_l5_exclus) if ratios_l5_exclus else 0,
            'ratio_l5_max_exclu': max(ratios_l5_exclus) if ratios_l5_exclus else 0
        }

        return analyse

    def _analyser_impact_decroissance_l4(self, simulations_brutes: List[SimulationMainN1],
                                       simulations_conformes: List[SimulationMainN1],
                                       ratio_l4_actuel: float) -> Dict:
        """
        Analyse l'impact de la contrainte décroissance L4 sur les simulations

        Args:
            simulations_brutes: Toutes les simulations générées
            simulations_conformes: Simulations respectant la décroissance L4
            ratio_l4_actuel: Ratio L4 de la main actuelle

        Returns:
            Dict: Statistiques sur l'impact de la contrainte
        """
        nb_brutes = len(simulations_brutes)
        nb_conformes = len(simulations_conformes)
        nb_exclues = nb_brutes - nb_conformes

        # Analyser les simulations exclues
        simulations_exclues = [s for s in simulations_brutes if s.ratio_l4_simule > ratio_l4_actuel]

        # Statistiques des ratios L4
        ratios_l4_bruts = [s.ratio_l4_simule for s in simulations_brutes]
        ratios_l4_conformes = [s.ratio_l4_simule for s in simulations_conformes]
        ratios_l4_exclus = [s.ratio_l4_simule for s in simulations_exclues]

        analyse = {
            'nb_simulations_brutes': nb_brutes,
            'nb_simulations_conformes': nb_conformes,
            'nb_simulations_exclues': nb_exclues,
            'pourcentage_exclusion': (nb_exclues / nb_brutes * 100) if nb_brutes > 0 else 0,
            'ratio_l4_actuel': ratio_l4_actuel,
            'ratio_l4_min_brut': min(ratios_l4_bruts) if ratios_l4_bruts else 0,
            'ratio_l4_max_brut': max(ratios_l4_bruts) if ratios_l4_bruts else 0,
            'ratio_l4_min_conforme': min(ratios_l4_conformes) if ratios_l4_conformes else 0,
            'ratio_l4_max_conforme': max(ratios_l4_conformes) if ratios_l4_conformes else 0,
            'ratio_l4_min_exclu': min(ratios_l4_exclus) if ratios_l4_exclus else 0,
            'ratio_l4_max_exclu': max(ratios_l4_exclus) if ratios_l4_exclus else 0
        }

        return analyse

    def _fallback_vers_l4(self, etat_partie: EtatPartieActuel,
                         simulations_brutes: List[SimulationMainN1]) -> PredictionTempsReel:
        """
        Fallback vers contrainte décroissance L4 quand L5 échoue

        Args:
            etat_partie: État actuel de la partie
            simulations_brutes: Toutes les simulations générées

        Returns:
            PredictionTempsReel: Prédiction basée sur L4 ou WAIT
        """
        print("🔄 SWITCH vers contrainte décroissance L4...")

        # Appliquer la contrainte décroissance L4
        simulations_conformes_l4 = []
        for simulation in simulations_brutes:
            if simulation.ratio_l4_simule <= etat_partie.ratio_l4_actuel:
                simulations_conformes_l4.append(simulation)

        if not simulations_conformes_l4:
            print("⚠️ AUCUNE SIMULATION CONFORME À LA DÉCROISSANCE L4 NON PLUS")
            print("🛑 RECOMMANDATION: WAIT - Situation trop incertaine pour prédire")

            return PredictionTempsReel(
                prediction_finale="WAIT",
                probabilite=0.0,
                confiance="AUCUNE",
                nb_simulations_s=0,
                nb_simulations_o=0,
                nb_simulations_total=len(simulations_brutes),
                simulations_detaillees=[],
                conditions_activees=[],
                justification="Aucune simulation conforme aux contraintes décroissance L5 et L4 - Situation trop incertaine"
            )

        print(f"✅ {len(simulations_conformes_l4)} simulations conformes à la décroissance L4")

        # Analyser l'impact de la contrainte décroissance L4
        analyse_decroissance_l4 = self._analyser_impact_decroissance_l4(
            simulations_brutes, simulations_conformes_l4, etat_partie.ratio_l4_actuel
        )
        print(f"📊 Exclusion L4: {analyse_decroissance_l4['pourcentage_exclusion']:.1f}% des simulations")
        print(f"📊 Ratio L4 actuel: {analyse_decroissance_l4['ratio_l4_actuel']:.6f}")

        # Extraire les INDEX1 des simulations L4 conformes (plus de TIE possible)
        resultats_index1 = []

        for simulation in simulations_conformes_l4:
            index1 = self._extraire_index3_depuis_index5(simulation.index5_simule)
            resultats_index1.append(index1)

        # Compter les résultats BANKER/PLAYER
        comptage_vote = {
            'BANKER': resultats_index1.count('BANKER'),
            'PLAYER': resultats_index1.count('PLAYER')
        }

        print(f"📊 Comptage L4 - BANKER: {comptage_vote['BANKER']}, PLAYER: {comptage_vote['PLAYER']}, TIE: 0 (exclus dès génération)")

        # Déterminer la prédiction avec L4 (pas de fallback supplémentaire)
        return self._determiner_prediction_majoritaire_simplifie(
            comptage_vote, simulations_conformes_l4, etat_partie, simulations_brutes, "L4"
        )

    def _determiner_prediction_majoritaire_avec_fallback(self, comptage_vote: Dict[str, int],
                                         comptage_tous: Dict[str, int],
                                         simulations: List[SimulationMainN1],
                                         etat_partie: EtatPartieActuel,
                                         simulations_brutes: List[SimulationMainN1],
                                         niveau_contrainte: str) -> PredictionTempsReel:
        """
        Détermine la prédiction finale avec logique de fallback intelligent

        Args:
            comptage_vote: Comptage BANKER/PLAYER pour le vote (TIE exclus)
            comptage_tous: Comptage complet BANKER/PLAYER/TIE (pour information)
            simulations: Liste des simulations utilisées
            etat_partie: État actuel de la partie
            simulations_brutes: Toutes les simulations générées
            niveau_contrainte: "L5" ou "L4" pour indiquer le niveau actuel

        Returns:
            PredictionTempsReel: Prédiction basée sur la majorité ou fallback
        """
        nb_simulations_total = len(simulations)
        nb_simulations_vote = sum(comptage_vote.values())  # Seulement BANKER + PLAYER

        # CAS 1: Toutes les simulations sont TIE
        if nb_simulations_vote == 0:
            if niveau_contrainte == "L5":
                print(f"⚠️ Toutes simulations {niveau_contrainte} sont TIE → Switch vers L4")
                return self._fallback_vers_l4(etat_partie, simulations_brutes)
            else:  # niveau_contrainte == "L4"
                print(f"⚠️ Toutes simulations {niveau_contrainte} sont TIE → Recommandation WAIT")
                return PredictionTempsReel(
                    prediction_finale="WAIT",
                    probabilite=0.0,
                    confiance="AUCUNE",
                    nb_simulations_s=0,
                    nb_simulations_o=0,
                    nb_simulations_total=nb_simulations_total,
                    simulations_detaillees=simulations,
                    conditions_activees=[],
                    justification=f"Toutes les simulations {niveau_contrainte} prédisent TIE - Situation trop incertaine"
                )

        # CAS 2: Égalité parfaite BANKER vs PLAYER
        elif comptage_vote['BANKER'] == comptage_vote['PLAYER']:
            if niveau_contrainte == "L5":
                print(f"⚠️ Égalité BANKER vs PLAYER en {niveau_contrainte} → Switch vers L4")
                return self._fallback_vers_l4(etat_partie, simulations_brutes)
            else:  # niveau_contrainte == "L4"
                print(f"⚠️ Égalité BANKER vs PLAYER en {niveau_contrainte} → Recommandation WAIT")
                return PredictionTempsReel(
                    prediction_finale="WAIT",
                    probabilite=50.0,
                    confiance="AUCUNE",
                    nb_simulations_s=0,
                    nb_simulations_o=0,
                    nb_simulations_total=nb_simulations_total,
                    simulations_detaillees=simulations,
                    conditions_activees=[],
                    justification=f"Égalité parfaite {niveau_contrainte}: {comptage_vote['BANKER']} BANKER vs {comptage_vote['PLAYER']} PLAYER - Situation trop incertaine"
                )

        # CAS 3: Majorité claire BANKER ou PLAYER
        else:
            if comptage_vote['BANKER'] > comptage_vote['PLAYER']:
                prediction = 'BANKER'
                nb_majoritaire = comptage_vote['BANKER']
            else:
                prediction = 'PLAYER'
                nb_majoritaire = comptage_vote['PLAYER']

            # Calculer la probabilité sur les votes valides (non-TIE)
            probabilite = (nb_majoritaire / nb_simulations_vote) * 100

            # Déterminer le niveau de confiance
            if probabilite >= 80:
                confiance = 'TRÈS_ÉLEVÉE'
            elif probabilite >= 70:
                confiance = 'ÉLEVÉE'
            elif probabilite >= 60:
                confiance = 'MODÉRÉE'
            else:
                confiance = 'FAIBLE'

            # Justification détaillée avec niveau de contrainte
            nb_tie = comptage_tous['TIE']
            if nb_tie > 0:
                justification = f"{nb_majoritaire}/{nb_simulations_vote} votes {niveau_contrainte} pour {prediction} ({probabilite:.1f}%) - {nb_tie} TIE exclus"
            else:
                justification = f"{nb_majoritaire}/{nb_simulations_vote} votes {niveau_contrainte} pour {prediction} ({probabilite:.1f}%)"

            print(f"✅ Majorité claire {niveau_contrainte}: {prediction} ({probabilite:.1f}%)")

            return PredictionTempsReel(
                prediction_finale=prediction,
                probabilite=probabilite,
                confiance=confiance,
                nb_simulations_s=0,  # Non utilisé dans la logique majoritaire
                nb_simulations_o=0,  # Non utilisé dans la logique majoritaire
                nb_simulations_total=nb_simulations_total,
                simulations_detaillees=simulations,
                conditions_activees=[],  # Non utilisé dans la logique majoritaire
                justification=justification
            )

    def _determiner_prediction_majoritaire_simplifie(self, comptage_vote: Dict[str, int],
                                                   simulations: List[SimulationMainN1],
                                                   etat_partie: EtatPartieActuel,
                                                   simulations_brutes: List[SimulationMainN1],
                                                   niveau_contrainte: str) -> PredictionTempsReel:
        """
        Détermine la prédiction finale avec logique simplifiée (TIE exclus dès génération)

        Args:
            comptage_vote: Comptage BANKER/PLAYER uniquement
            simulations: Liste des simulations utilisées
            etat_partie: État actuel de la partie
            simulations_brutes: Toutes les simulations générées
            niveau_contrainte: "L5" ou "L4" pour indiquer le niveau actuel

        Returns:
            PredictionTempsReel: Prédiction basée sur la majorité ou fallback
        """
        nb_simulations_total = len(simulations)
        nb_simulations_vote = sum(comptage_vote.values())  # BANKER + PLAYER

        # Plus de cas "toutes TIE" possible car TIE exclus dès la génération !

        # CAS 1: Égalité parfaite BANKER vs PLAYER
        if comptage_vote['BANKER'] == comptage_vote['PLAYER']:
            if niveau_contrainte == "L5":
                print(f"⚠️ Égalité BANKER vs PLAYER en {niveau_contrainte} → Switch vers L4")
                return self._fallback_vers_l4(etat_partie, simulations_brutes)
            else:  # niveau_contrainte == "L4"
                print(f"⚠️ Égalité BANKER vs PLAYER en {niveau_contrainte} → Recommandation WAIT")
                return PredictionTempsReel(
                    prediction_finale="WAIT",
                    probabilite=50.0,
                    confiance="AUCUNE",
                    nb_simulations_s=0,
                    nb_simulations_o=0,
                    nb_simulations_total=nb_simulations_total,
                    simulations_detaillees=simulations,
                    conditions_activees=[],
                    justification=f"Égalité parfaite {niveau_contrainte}: {comptage_vote['BANKER']} BANKER vs {comptage_vote['PLAYER']} PLAYER - Situation trop incertaine"
                )

        # CAS 2: Majorité claire BANKER ou PLAYER
        else:
            if comptage_vote['BANKER'] > comptage_vote['PLAYER']:
                prediction = 'BANKER'
                nb_majoritaire = comptage_vote['BANKER']
            else:
                prediction = 'PLAYER'
                nb_majoritaire = comptage_vote['PLAYER']

            # Calculer la probabilité sur les votes valides
            probabilite = (nb_majoritaire / nb_simulations_vote) * 100

            # Déterminer le niveau de confiance
            if probabilite >= 80:
                confiance = 'TRÈS_ÉLEVÉE'
            elif probabilite >= 70:
                confiance = 'ÉLEVÉE'
            elif probabilite >= 60:
                confiance = 'MODÉRÉE'
            else:
                confiance = 'FAIBLE'

            # Justification simplifiée
            justification = f"{nb_majoritaire}/{nb_simulations_vote} votes {niveau_contrainte} pour {prediction} ({probabilite:.1f}%)"

            print(f"✅ Majorité claire {niveau_contrainte}: {prediction} ({probabilite:.1f}%)")

            return PredictionTempsReel(
                prediction_finale=prediction,
                probabilite=probabilite,
                confiance=confiance,
                nb_simulations_s=0,  # Non utilisé dans la logique majoritaire
                nb_simulations_o=0,  # Non utilisé dans la logique majoritaire
                nb_simulations_total=nb_simulations_total,
                simulations_detaillees=simulations,
                conditions_activees=[],  # Non utilisé dans la logique majoritaire
                justification=justification
            )

    def _calculer_entropie_globale(self, sequence_index5: List[str]) -> float:
        """Calcule l'entropie globale de la séquence INDEX5 actuelle"""
        if len(sequence_index5) < 2:
            return 0.0

        # Calculer la distribution des valeurs INDEX5
        compteurs = {}
        for valeur in sequence_index5:
            compteurs[valeur] = compteurs.get(valeur, 0) + 1

        # Calculer l'entropie de Shannon
        total = len(sequence_index5)
        entropie = 0.0

        for count in compteurs.values():
            if count > 0:
                probabilite = count / total
                entropie -= probabilite * math.log2(probabilite)

        return entropie

    def _simuler_main_n1(self, etat_partie: EtatPartieActuel,
                        index5_simule: str) -> SimulationMainN1:
        """Simule une possibilité pour la main n+1"""

        # 1. Créer la séquence étendue avec la valeur simulée
        sequence_etendue = etat_partie.sequence_index5 + [index5_simule]

        # 2. Calculer l'entropie globale étendue (main 1 → main n+1)
        entropie_globale_etendue = self._calculer_entropie_globale(sequence_etendue)

        # 3. Calculer les signatures L4 et L5 pour la main n+1
        ratio_l4_simule = self._calculer_ratio_l4_main_n1(sequence_etendue, entropie_globale_etendue)
        ratio_l5_simule = self._calculer_ratio_l5_main_n1(sequence_etendue, entropie_globale_etendue)

        # 4. Calculer DIFF, DIFF_L4, DIFF_L5
        diff_simule = abs(ratio_l4_simule - ratio_l5_simule)
        diff_l4_simule = abs(ratio_l4_simule - etat_partie.ratio_l4_actuel)
        diff_l5_simule = abs(ratio_l5_simule - etat_partie.ratio_l5_actuel)

        # 5. Déterminer INDEX1 correspondant (INDEX3 depuis INDEX5)
        index1_simule = self._extraire_index3_depuis_index5(index5_simule)

        # 6. Calculer le pattern S/O/E simulé
        pattern_soe_simule = self._calculer_pattern_soe_simule(
            etat_partie.sequence_index1, index1_simule
        )

        return SimulationMainN1(
            index5_simule=index5_simule,
            index1_simule=index1_simule,
            ratio_l4_simule=ratio_l4_simule,
            ratio_l5_simule=ratio_l5_simule,
            diff_simule=diff_simule,
            diff_l4_simule=diff_l4_simule,
            diff_l5_simule=diff_l5_simule,
            pattern_soe_simule=pattern_soe_simule
        )

    def _calculer_ratio_l4_main_n1(self, sequence_etendue: List[str],
                                  entropie_globale: float) -> float:
        """Calcule le ratio L4 pour la main n+1"""
        if len(sequence_etendue) < 4:
            return 0.0

        # Extraire la séquence L4 (4 dernières valeurs)
        sequence_l4 = sequence_etendue[-4:]

        # Calculer la signature entropique L4
        signature_l4 = self._calculer_signature_entropique(sequence_l4)

        # Calculer le ratio L4/Global
        if entropie_globale > 0:
            return signature_l4 / entropie_globale
        else:
            return 0.0

    def _calculer_ratio_l5_main_n1(self, sequence_etendue: List[str],
                                  entropie_globale: float) -> float:
        """Calcule le ratio L5 pour la main n+1"""
        if len(sequence_etendue) < 5:
            return 0.0

        # Extraire la séquence L5 (5 dernières valeurs)
        sequence_l5 = sequence_etendue[-5:]

        # Calculer la signature entropique L5
        signature_l5 = self._calculer_signature_entropique(sequence_l5)

        # Calculer le ratio L5/Global
        if entropie_globale > 0:
            return signature_l5 / entropie_globale
        else:
            return 0.0

    def _calculer_signature_entropique(self, sequence: List[str]) -> float:
        """Calcule la signature entropique d'une séquence"""
        if len(sequence) < 2:
            return 0.0

        # Calculer la distribution
        compteurs = {}
        for valeur in sequence:
            compteurs[valeur] = compteurs.get(valeur, 0) + 1

        # Calculer l'entropie de Shannon
        total = len(sequence)
        entropie = 0.0

        for count in compteurs.values():
            if count > 0:
                probabilite = count / total
                entropie -= probabilite * math.log2(probabilite)

        return entropie

    def _calculer_pattern_soe_simule(self, sequence_index1_actuelle: List[str],
                                   index1_simule: str) -> str:
        """Calcule le pattern S/O/E simulé pour la main n+1"""
        if len(sequence_index1_actuelle) == 0:
            return '--'  # Indéterminé

        # Prendre le dernier résultat non-TIE
        dernier_non_tie = None
        for i in range(len(sequence_index1_actuelle) - 1, -1, -1):
            if sequence_index1_actuelle[i] != 'TIE':
                dernier_non_tie = sequence_index1_actuelle[i]
                break

        if dernier_non_tie is None:
            return '--'  # Aucun résultat non-TIE trouvé

        # Calculer le pattern
        if index1_simule == 'TIE':
            return 'E'  # Égalité
        elif index1_simule == dernier_non_tie:
            return 'S'  # Same (continuation)
        else:
            return 'O'  # Opposite (alternance)

    # [LEGACY] Méthodes d'évaluation S/O supprimées - Remplacées par logique majoritaire



    # [LEGACY] Ancienne méthode de détermination S/O supprimée - Remplacée par logique majoritaire

    def afficher_details_simulation(self, prediction: PredictionTempsReel,
                                  nb_details: int = 5):
        """Affiche les détails des simulations les plus intéressantes"""
        print(f"\n📊 DÉTAILS DES SIMULATIONS (TOP {nb_details})")
        print("=" * 60)

        # Trier les simulations par pertinence
        simulations_triees = sorted(
            prediction.simulations_detaillees,
            key=lambda s: abs(s.diff_simule) + abs(s.diff_l4_simule) + abs(s.diff_l5_simule),
            reverse=True
        )

        print("INDEX5 | INDEX1  | RATIO_L4 | RATIO_L5 | DIFF   | DIFF_L4 | DIFF_L5 | S/O/E")
        print("-" * 75)

        for i, sim in enumerate(simulations_triees[:nb_details]):
            print(f"{sim.index5_simule:>6} | {sim.index1_simule:<7} | "
                  f"{sim.ratio_l4_simule:>8.3f} | {sim.ratio_l5_simule:>8.3f} | "
                  f"{sim.diff_simule:>6.3f} | {sim.diff_l4_simule:>7.3f} | "
                  f"{sim.diff_l5_simule:>7.3f} | {sim.pattern_soe_simule:>5}")

    def generer_rapport_prediction(self, prediction: PredictionTempsReel,
                                 etat_partie: EtatPartieActuel) -> str:
        """Génère un rapport détaillé de la prédiction"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        nom_fichier = f"prediction_temps_reel_{timestamp}.txt"

        with open(nom_fichier, 'w', encoding='utf-8') as f:
            f.write("RAPPORT PRÉDICTION TEMPS RÉEL ULTRA\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Main actuelle: {etat_partie.main_actuelle}\n")
            f.write(f"Main prédite: {etat_partie.main_actuelle + 1}\n\n")

            # État actuel
            f.write("ÉTAT ACTUEL DE LA PARTIE:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Séquence INDEX5: {etat_partie.sequence_index5[-10:]}\n")  # 10 dernières
            f.write(f"Séquence INDEX1: {etat_partie.sequence_index1[-10:]}\n")  # 10 dernières
            f.write(f"RATIO_L4 actuel: {etat_partie.ratio_l4_actuel:.6f}\n")
            f.write(f"RATIO_L5 actuel: {etat_partie.ratio_l5_actuel:.6f}\n")
            f.write(f"DIFF actuel: {abs(etat_partie.ratio_l4_actuel - etat_partie.ratio_l5_actuel):.6f}\n\n")

            # Prédiction
            f.write("PRÉDICTION FINALE:\n")
            f.write("-" * 20 + "\n")
            f.write(f"Prédiction: {prediction.prediction_finale}\n")
            f.write(f"Probabilité: {prediction.probabilite:.1f}%\n")
            f.write(f"Confiance: {prediction.confiance}\n")
            f.write(f"Justification: {prediction.justification}\n\n")

            # Statistiques simulations
            f.write("STATISTIQUES SIMULATIONS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Total simulations: {prediction.nb_simulations_total}\n")
            f.write(f"Favorables S: {prediction.nb_simulations_s}\n")
            f.write(f"Favorables O: {prediction.nb_simulations_o}\n")
            f.write(f"Neutres: {prediction.nb_simulations_total - prediction.nb_simulations_s - prediction.nb_simulations_o}\n\n")

            # Conditions activées
            if prediction.conditions_activees:
                f.write("CONDITIONS PRÉDICTIVES ACTIVÉES:\n")
                f.write("-" * 35 + "\n")
                for condition in prediction.conditions_activees:
                    f.write(f"• {condition}\n")
                f.write("\n")

            # Détails simulations
            f.write("DÉTAILS TOUTES LES SIMULATIONS:\n")
            f.write("-" * 35 + "\n")
            f.write("INDEX5 | INDEX1  | RATIO_L4 | RATIO_L5 | DIFF   | DIFF_L4 | DIFF_L5 | S/O/E\n")
            f.write("-" * 75 + "\n")

            for sim in prediction.simulations_detaillees:
                f.write(f"{sim.index5_simule:>6} | {sim.index1_simule:<7} | "
                       f"{sim.ratio_l4_simule:>8.6f} | {sim.ratio_l5_simule:>8.6f} | "
                       f"{sim.diff_simule:>6.6f} | {sim.diff_l4_simule:>7.6f} | "
                       f"{sim.diff_l5_simule:>7.6f} | {sim.pattern_soe_simule:>5}\n")

        return nom_fichier

def tester_predicteur_temps_reel():
    """Test du prédicteur temps réel ultra"""
    print("🧪 TEST PRÉDICTEUR TEMPS RÉEL ULTRA")
    print("=" * 60)

    try:
        # Initialiser le prédicteur
        predicteur = PredicteurTempsReelUltra()

        # Créer un état de partie de test avec format INDEX5 correct
        etat_test = EtatPartieActuel(
            sequence_index5=['0_A_PLAYER', '1_B_BANKER', '0_C_TIE', '1_A_PLAYER', '0_B_BANKER',
                           '1_C_TIE', '0_A_PLAYER', '1_B_BANKER', '0_C_TIE', '1_A_PLAYER'],  # 10 mains
            sequence_index1=['PLAYER', 'BANKER', 'TIE', 'PLAYER', 'BANKER', 'TIE', 'PLAYER', 'BANKER', 'TIE', 'PLAYER'],
            main_actuelle=10,
            ratio_l4_actuel=0.75,
            ratio_l5_actuel=0.82,
            ratio_l4_precedent=0.68,
            ratio_l5_precedent=0.79
        )

        print(f"\n📊 ÉTAT DE TEST:")
        print(f"   Main actuelle: {etat_test.main_actuelle}")
        print(f"   Séquence INDEX5: {etat_test.sequence_index5}")
        print(f"   Séquence INDEX1: {etat_test.sequence_index1}")
        print(f"   RATIO_L4: {etat_test.ratio_l4_actuel:.3f}")
        print(f"   RATIO_L5: {etat_test.ratio_l5_actuel:.3f}")

        # Effectuer la prédiction
        prediction = predicteur.predire_main_suivante(etat_test)

        # Afficher les résultats
        print(f"\n🎯 RÉSULTATS PRÉDICTION:")
        print(f"   Prédiction: {prediction.prediction_finale}")
        print(f"   Probabilité: {prediction.probabilite:.1f}%")
        print(f"   Confiance: {prediction.confiance}")
        print(f"   Simulations S: {prediction.nb_simulations_s}")
        print(f"   Simulations O: {prediction.nb_simulations_o}")
        print(f"   Conditions: {len(prediction.conditions_activees)}")

        # Afficher les détails
        predicteur.afficher_details_simulation(prediction, 5)

        # Générer le rapport
        rapport = predicteur.generer_rapport_prediction(prediction, etat_test)
        print(f"\n📄 Rapport généré: {rapport}")

        return True

    except Exception as e:
        print(f"❌ Erreur test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 PRÉDICTEUR TEMPS RÉEL ULTRA-SOPHISTIQUÉ")
    print("=" * 70)
    print("🎯 Simulation complète de toutes les possibilités main n+1")
    print("📊 Calculs entropiques L4/L5 en temps réel")
    print("🔮 Prédiction S/O basée sur tableau prédictif complet")
    print("=" * 70)

    # Test du prédicteur
    success = tester_predicteur_temps_reel()

    if success:
        print(f"\n🎯 PRÉDICTEUR TEMPS RÉEL OPÉRATIONNEL !")
        print("✅ Simulation de toutes les possibilités INDEX5")
        print("✅ Calculs entropiques L4/L5 temps réel")
        print("✅ Prédiction S/O ultra-sophistiquée")
        print("🚀 SYSTÈME RÉVOLUTIONNAIRE PRÊT !")
    else:
        print(f"\n❌ ERREUR PRÉDICTEUR TEMPS RÉEL")
        print("⚠️ Vérifiez les dépendances et modules")

    print("\n" + "=" * 70)