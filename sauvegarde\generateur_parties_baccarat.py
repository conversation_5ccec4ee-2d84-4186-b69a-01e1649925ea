#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GÉNÉRATEUR DE PARTIES BACCARAT LUPASCO RÉALISTE
===============================================

Génère des parties de baccarat réalistes avec :
- Sabot de 416 cartes (8 decks de 52 cartes)
- Règles strictes du baccarat selon regles_baccarat_essentielles.txt
- Simulation complète des tirages de cartes
- Calcul automatique des INDEX Lupasco
- Export CSV pour analyse

Basé sur les règles officielles du baccarat, sans statistiques prédéfinies.
"""

import secrets  # CSPRNG cryptographiquement sécurisé
import os       # Entropie système
import hashlib  # Combinaison d'entropie
import time     # Timing haute précision
import json     # Pour export JSON
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime

# ============================================================================
# CONFIGURATION DU SABOT BACCARAT
# ============================================================================

# Sabot de 8 decks standard
DECKS_COUNT = 8
CARDS_PER_DECK = 52
TOTAL_CARDS = DECKS_COUNT * CARDS_PER_DECK  # 416 cartes

# Cut card à 80% du sabot
CUT_CARD_POSITION = int(TOTAL_CARDS * 0.8)  # 332ème carte

# Valeurs des cartes pour le calcul des scores
CARD_VALUES = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 0, 'J': 0, 'Q': 0, 'K': 0
}

# Cartes pour le brûlage (valeur → nombre de cartes supplémentaires)
BURN_CARDS_COUNT = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 10, 'J': 10, 'Q': 10, 'K': 10
}

# Mapping nombre de cartes → catégories INDEX2
CARDS_MAPPING = {
    4: 'A',  # pair_4 = A
    5: 'C',  # impair_5 = C
    6: 'B'   # pair_6 = B
}

# ============================================================================
# STRUCTURES DE DONNÉES
# ============================================================================

@dataclass
class Carte:
    """Représente une carte du sabot"""
    rang: str      # 'A', '2', '3', ..., '10', 'J', 'Q', 'K'
    couleur: str   # '♠', '♥', '♦', '♣'
    valeur: int    # Valeur pour le calcul du score (0-9)

@dataclass
class MainBaccarat:
    """Structure d'une main de baccarat avec tous les INDEX Lupasco"""

    # Identification
    main_number: int
    manche_pb_number: Optional[int]  # None pour brûlage, même numéro pour TIE

    # Cartes distribuées
    cartes_player: List[Carte]
    cartes_banker: List[Carte]
    total_cartes_distribuees: int

    # Scores calculés
    score_player: int
    score_banker: int

    # INDEX Lupasco
    index1_sync_state: int           # 0 (SYNC) ou 1 (DESYNC)
    index2_cards_count: int          # 4, 5, ou 6 (ou nombre pour brûlage)
    index2_cards_category: str       # 'A', 'C', 'B' (ou '' pour brûlage)
    index3_result: str               # 'PLAYER', 'BANKER', 'TIE' (ou '' pour brûlage)
    index5_combined: str             # INDEX1_INDEX2_INDEX3 (ex: '0_A_PLAYER')

    # Métadonnées
    timestamp: str = ""

@dataclass
class PartieBaccarat:
    """Structure d'une partie complète de baccarat"""

    # Identification
    partie_number: int

    # Sabot
    sabot_initial: List[Carte]
    cartes_restantes: int

    # Brûlage initial
    cartes_brulees: List[Carte]
    burn_cards_count: int
    burn_parity: str                 # 'PAIR' ou 'IMPAIR'
    initial_sync_state: int          # 0 (SYNC) ou 1 (DESYNC)

    # Mains de la partie
    mains: List[MainBaccarat]

    # Paramètres avec valeurs par défaut
    cut_card_atteinte: bool = False
    total_mains: int = 0
    total_manches_pb: int = 0
    total_ties: int = 0
    current_sync_state: int = 0  # SYNC
    premiere_carte_brulee: Optional[Carte] = None

# ============================================================================
# GÉNÉRATEUR DE HASARD CRYPTOGRAPHIQUEMENT SÉCURISÉ
# ============================================================================

class SecureRandomGenerator:
    """
    Générateur de hasard cryptographiquement sécurisé

    Combine plusieurs sources d'entropie pour un hasard optimal :
    - CSPRNG système (secrets)
    - Entropie hardware (os.urandom)
    - Timing haute précision
    - Adresses mémoire
    """

    def __init__(self, seed: Optional[int] = None):
        """Initialise le générateur sécurisé"""
        # Collecter entropie de multiples sources
        entropy_sources = [
            secrets.token_bytes(32),      # CSPRNG système
            os.urandom(32),               # Entropie hardware
            str(time.time_ns()).encode(), # Timing haute précision
            str(id(self)).encode(),       # Adresse mémoire
        ]

        # Si seed fourni, l'ajouter aux sources
        if seed is not None:
            entropy_sources.append(str(seed).encode())

        # Combiner toutes les sources avec SHA-256
        combined = b''.join(entropy_sources)
        self.entropy_pool = hashlib.sha256(combined).digest()

        print(f"Generateur securise initialise avec {len(entropy_sources)} sources d'entropie")

    def secure_shuffle(self, deck: List) -> List:
        """
        Mélange cryptographiquement sécurisé (Fisher-Yates)

        Args:
            deck: Liste à mélanger

        Returns:
            List: Liste mélangée de façon sécurisée
        """
        deck_copy = deck.copy()

        for i in range(len(deck_copy) - 1, 0, -1):
            # Générer index aléatoire sécurisé
            random_bytes = secrets.token_bytes(4)
            random_int = int.from_bytes(random_bytes, 'big')
            j = random_int % (i + 1)

            # Échanger les éléments
            deck_copy[i], deck_copy[j] = deck_copy[j], deck_copy[i]

        return deck_copy

    def refresh_entropy(self):
        """Rafraîchit le pool d'entropie"""
        new_entropy = [
            secrets.token_bytes(16),
            str(time.time_ns()).encode(),
            os.urandom(16)
        ]
        combined = self.entropy_pool + b''.join(new_entropy)
        self.entropy_pool = hashlib.sha256(combined).digest()

# ============================================================================
# GÉNÉRATEUR DE PARTIES RÉALISTE
# ============================================================================

class GenerateurPartiesBaccarat:
    """
    Générateur de parties de baccarat réalistes avec hasard cryptographiquement sécurisé

    Simule un vrai sabot de 416 cartes avec les règles strictes du baccarat.
    Utilise un générateur de hasard sécurisé pour un mélange optimal.
    Aucune statistique prédéfinie - tout est calculé selon les tirages réels.
    """

    def __init__(self, seed: Optional[int] = None):
        """
        Initialise le générateur

        Args:
            seed: Graine pour la reproductibilité (optionnel)
        """
        # Initialiser le générateur sécurisé
        self.secure_rng = SecureRandomGenerator(seed)

        self.parties_generees = []

    def _creer_sabot_complet(self) -> List[Carte]:
        """
        Crée un sabot complet de 416 cartes (8 decks mélangés cryptographiquement)

        Returns:
            List[Carte]: Sabot mélangé de façon cryptographiquement sécurisée
        """
        sabot = []
        couleurs = ['♠', '♥', '♦', '♣']
        rangs = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

        # Créer 8 decks complets (416 cartes exactement)
        for deck in range(DECKS_COUNT):
            for couleur in couleurs:
                for rang in rangs:
                    carte = Carte(
                        rang=rang,
                        couleur=couleur,
                        valeur=CARD_VALUES[rang]
                    )
                    sabot.append(carte)

        print(f"Sabot cree : {len(sabot)} cartes")

        # Rafraîchir l'entropie avant mélange
        self.secure_rng.refresh_entropy()

        # Mélanger le sabot avec algorithme cryptographiquement sécurisé
        sabot_melange = self.secure_rng.secure_shuffle(sabot)

        print(f"Melange cryptographiquement securise effectue")

        return sabot_melange
    
    def _effectuer_brulage(self, sabot: List[Carte]) -> Tuple[List[Carte], int, str, str]:
        """
        Effectue le brûlage selon les règles officielles du baccarat

        Args:
            sabot: Sabot complet de cartes

        Returns:
            Tuple[cartes_brulees, cards_count, parity, sync_state]
        """
        if len(sabot) == 0:
            raise ValueError("Sabot vide pour le brûlage")

        # Tirer la première carte pour déterminer le nombre de cartes à brûler
        premiere_carte = sabot.pop(0)
        cartes_brulees = [premiere_carte]

        print(f"🔥 Première carte brûlée : {premiere_carte.rang}{premiere_carte.couleur}")

        # Nombre de cartes supplémentaires à brûler selon les règles
        cartes_supplementaires = BURN_CARDS_COUNT[premiere_carte.rang]

        # Calculer le total attendu
        total_attendu = 1 + cartes_supplementaires  # 1 carte tirée + cartes supplémentaires

        print(f"🔥 Cartes supplémentaires à brûler : {cartes_supplementaires}")
        print(f"🔥 Total cartes à brûler : {total_attendu}")

        # Brûler les cartes supplémentaires
        for i in range(cartes_supplementaires):
            if len(sabot) > 0:
                carte_brulee = sabot.pop(0)
                cartes_brulees.append(carte_brulee)
                print(f"🔥 Carte brûlée {i+2}/{total_attendu} : {carte_brulee.rang}{carte_brulee.couleur}")

        # Total de cartes brûlées (vérification)
        total_brulees = len(cartes_brulees)

        # Parité du total brûlé
        burn_parity = 'PAIR' if total_brulees % 2 == 0 else 'IMPAIR'

        # État SYNC initial selon la parité (RÈGLE CORRECTE)
        # Si total brûlé PAIR → première manche commence en SYNC (0)
        # Si total brûlé IMPAIR → première manche commence en DESYNC (1)
        initial_sync = 0 if burn_parity == 'PAIR' else 1

        print(f"🔥 Parité brûlage : {burn_parity} → Première manche : {initial_sync}")

        return cartes_brulees, total_brulees, burn_parity, initial_sync
    
    def _calculer_score(self, cartes: List[Carte]) -> int:
        """
        Calcule le score d'une main selon les règles du baccarat

        Args:
            cartes: Liste des cartes de la main

        Returns:
            int: Score de la main (0-9)
        """
        total = sum(carte.valeur for carte in cartes)
        return total % 10

    def _doit_tirer_player(self, score_player: int) -> bool:
        """
        Détermine si le joueur doit tirer une 3ème carte

        Args:
            score_player: Score actuel du joueur

        Returns:
            bool: True si le joueur doit tirer
        """
        if score_player in [8, 9]:  # Naturel
            return False
        elif score_player in [0, 1, 2, 3, 4, 5]:  # Tire
            return True
        else:  # 6, 7 - Reste
            return False

    def _doit_tirer_banker(self, score_banker: int,
                          troisieme_carte_player: Optional[Carte]) -> bool:
        """
        Détermine si le banquier doit tirer une 3ème carte

        Args:
            score_banker: Score actuel du banquier
            troisieme_carte_player: 3ème carte du joueur (None si pas tirée)

        Returns:
            bool: True si le banquier doit tirer
        """
        if score_banker in [8, 9]:  # Naturel
            return False

        if troisieme_carte_player is None:  # Joueur n'a pas tiré
            if score_banker in [0, 1, 2, 3, 4, 5]:
                return True
            else:  # 6, 7
                return False
        else:  # Joueur a tiré une 3ème carte
            valeur_3eme = troisieme_carte_player.valeur

            if score_banker == 3:
                return valeur_3eme != 8
            elif score_banker == 4:
                return valeur_3eme in [2, 3, 4, 5, 6, 7]
            elif score_banker == 5:
                return valeur_3eme in [4, 5, 6, 7]
            elif score_banker == 6:
                return valeur_3eme in [6, 7]
            elif score_banker == 7:
                return False
            else:  # 0, 1, 2
                return True
    
    def _jouer_manche(self, sabot: List[Carte]) -> Tuple[List[Carte], List[Carte], int, str]:
        """
        Joue une manche complète de baccarat selon les règles officielles

        Args:
            sabot: Sabot de cartes (modifié en place)

        Returns:
            Tuple[cartes_player, cartes_banker, total_cartes, resultat]
        """
        if len(sabot) < 6:  # Sécurité - minimum 6 cartes pour une manche complète
            raise ValueError("Pas assez de cartes dans le sabot")

        # Distribution initiale : 2 cartes chacun
        cartes_player = [sabot.pop(0), sabot.pop(0)]
        cartes_banker = [sabot.pop(0), sabot.pop(0)]

        # Calcul des scores initiaux
        score_player = self._calculer_score(cartes_player)
        score_banker = self._calculer_score(cartes_banker)

        # Vérifier les naturels (8 ou 9)
        if score_player in [8, 9] or score_banker in [8, 9]:
            # Naturel - pas de 3ème carte
            pass
        else:
            # Règles de tirage
            troisieme_carte_player = None

            # Le joueur tire-t-il ?
            if self._doit_tirer_player(score_player):
                troisieme_carte_player = sabot.pop(0)
                cartes_player.append(troisieme_carte_player)
                score_player = self._calculer_score(cartes_player)

            # Le banquier tire-t-il ?
            if self._doit_tirer_banker(score_banker, troisieme_carte_player):
                cartes_banker.append(sabot.pop(0))
                score_banker = self._calculer_score(cartes_banker)

        # Déterminer le résultat
        if score_player > score_banker:
            resultat = 'PLAYER'
        elif score_banker > score_player:
            resultat = 'BANKER'
        else:
            resultat = 'TIE'

        # Total de cartes distribuées
        total_cartes = len(cartes_player) + len(cartes_banker)

        return cartes_player, cartes_banker, total_cartes, resultat

    def _calculer_nouvel_etat_sync(self, etat_actuel: int, cards_parity: str) -> int:
        """
        Calcule le nouvel état SYNC/DESYNC selon les règles Lupasco

        Args:
            etat_actuel: État SYNC/DESYNC actuel (0=SYNC, 1=DESYNC)
            cards_parity: Parité des cartes ('PAIR' ou 'IMPAIR')

        Returns:
            int: Nouvel état SYNC/DESYNC (0=SYNC, 1=DESYNC)
        """
        if cards_parity == 'PAIR':
            # PAIR conserve l'état
            return etat_actuel
        else:
            # IMPAIR change l'état
            return 1 if etat_actuel == 0 else 0
    
    def generer_partie(self, partie_number: int, max_manches: int = 60) -> PartieBaccarat:
        """
        Génère une partie complète de baccarat réaliste avec hasard cryptographiquement sécurisé

        Args:
            partie_number: Numéro de la partie
            max_manches: Nombre maximum de manches P/B (défaut: 60)

        Returns:
            PartieBaccarat: Partie générée avec mélange sécurisé
        """
        print(f"\n🎲 Génération partie {partie_number} avec hasard cryptographiquement sécurisé...")

        # Créer et mélanger le sabot de façon sécurisée
        sabot = self._creer_sabot_complet()

        # Effectuer le brûlage
        cartes_brulees, burn_cards_count, burn_parity, initial_sync = self._effectuer_brulage(sabot)

        # Créer la partie
        partie = PartieBaccarat(
            partie_number=partie_number,
            sabot_initial=sabot.copy(),  # Copie pour référence
            cartes_restantes=len(sabot),
            cartes_brulees=cartes_brulees,
            burn_cards_count=burn_cards_count,
            burn_parity=burn_parity,
            initial_sync_state=initial_sync,
            mains=[],
            current_sync_state=initial_sync
        )

        # Stocker les informations de brûlage dans la partie (pas comme une main)
        partie.premiere_carte_brulee = cartes_brulees[0] if cartes_brulees else None

        # Jouer les manches jusqu'à EXACTEMENT max_manches P/B (priorité absolue)
        main_counter = 1  # Commencer à 1 (plus de main 0 pour brûlage)
        manche_pb_counter = 1  # Commencer à manche 1 (avant distribution)

        while manche_pb_counter <= max_manches:  # CONDITION PRINCIPALE : 60 manches P/B

            # Vérifier s'il reste assez de cartes pour une manche
            if len(sabot) < 6:
                print(f"ARRET FORCE : plus assez de cartes (manches P/B: {manche_pb_counter}/{max_manches})")
                break

            # Vérifier si cut card atteinte (332 cartes distribuées) - INFORMATIF SEULEMENT
            cartes_distribuees = TOTAL_CARDS - len(sabot)
            if cartes_distribuees >= CUT_CARD_POSITION and not partie.cut_card_atteinte:
                print(f"Cut card atteinte : {cartes_distribuees} cartes distribuees")
                print(f"Continuation pour atteindre {max_manches} manches P/B (manches restantes: {max_manches - manche_pb_counter})")
                partie.cut_card_atteinte = True

            try:
                # Jouer une manche
                cartes_player, cartes_banker, total_cartes, result = self._jouer_manche(sabot)

                # Calculer les scores finaux
                score_player = self._calculer_score(cartes_player)
                score_banker = self._calculer_score(cartes_banker)

                # Déterminer la catégorie INDEX2
                cards_category = CARDS_MAPPING.get(total_cartes, f"cartes_{total_cartes}")
                cards_parity = 'PAIR' if total_cartes % 2 == 0 else 'IMPAIR'

                # État SYNC au début de cette main
                current_sync = partie.current_sync_state

                # Calculer le numéro de manche P/B selon la nouvelle logique
                if result in ['PLAYER', 'BANKER']:
                    # PLAYER ou BANKER : utiliser le compteur actuel puis l'incrémenter
                    manche_pb_number = manche_pb_counter
                    manche_pb_counter += 1  # Incrémenter pour la prochaine manche P/B
                else:  # TIE
                    # TIE : utiliser le compteur actuel SANS l'incrémenter
                    manche_pb_number = manche_pb_counter

                # Créer la main
                main = MainBaccarat(
                    main_number=main_counter,
                    manche_pb_number=manche_pb_number,
                    cartes_player=cartes_player,
                    cartes_banker=cartes_banker,
                    total_cartes_distribuees=total_cartes,
                    score_player=score_player,
                    score_banker=score_banker,
                    index1_sync_state=current_sync,
                    index2_cards_count=total_cartes,
                    index2_cards_category=cards_category,
                    index3_result=result,
                    index5_combined=f"{current_sync}_{cards_category}_{result}",
                    timestamp=datetime.now().isoformat()
                )

                partie.mains.append(main)

                # Mettre à jour l'état pour la prochaine main
                partie.current_sync_state = self._calculer_nouvel_etat_sync(current_sync, cards_parity)

                # Mettre à jour les statistiques
                partie.total_mains += 1
                if result in ['PLAYER', 'BANKER']:
                    partie.total_manches_pb += 1
                else:
                    partie.total_ties += 1

                main_counter += 1
                partie.cartes_restantes = len(sabot)

                # Vérifier si on a atteint exactement max_manches P/B
                if manche_pb_counter > max_manches:
                    print(f"Objectif atteint : {max_manches} manches P/B completees")
                    break

            except ValueError as e:
                # Plus assez de cartes
                print(f"Fin de partie forcee : {e}")
                print(f"Manches P/B realisees : {manche_pb_counter}/{max_manches}")
                break

        # Afficher le résumé de la partie
        manches_realisees = manche_pb_counter - 1  # -1 car le compteur est toujours en avance
        if manches_realisees == max_manches:
            print(f"Partie {partie_number} terminee : {max_manches} manches P/B (objectif atteint)")
        else:
            print(f"Partie {partie_number} terminee : {manches_realisees}/{max_manches} manches P/B")

        return partie
    
    def generer_multiple_parties(self, nb_parties: int, max_manches: int = 60) -> List[PartieBaccarat]:
        """
        Génère plusieurs parties
        
        Args:
            nb_parties: Nombre de parties à générer
            max_manches: Nombre maximum de manches P/B par partie
        
        Returns:
            List[PartieBaccarat]: Liste des parties générées
        """
        parties = []
        
        for i in range(1, nb_parties + 1):
            partie = self.generer_partie(i, max_manches)
            parties.append(partie)
            self.parties_generees.append(partie)
            
            # Affichage détaillé du résultat
            if partie.total_manches_pb == max_manches:
                print(f"✅ Partie {i}/{nb_parties} - {partie.total_manches_pb} manches P/B, {partie.total_ties} TIE (OBJECTIF ATTEINT)")
            else:
                print(f"⚠️  Partie {i}/{nb_parties} - {partie.total_manches_pb} manches P/B, {partie.total_ties} TIE (INCOMPLET)")
                if partie.cut_card_atteinte:
                    print(f"   → Cut card atteinte, cartes insuffisantes")
        
        return parties
    
    def exporter_txt(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties en format texte (.txt) selon le format demandé

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier TXT
        """
        with open(filename, 'w', encoding='utf-8') as txtfile:
            # En-tête du fichier
            txtfile.write("GÉNÉRATEUR PARTIES BACCARAT LUPASCO\n")
            txtfile.write("=" * 50 + "\n")
            txtfile.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txtfile.write(f"Nombre de parties : {len(parties)}\n")
            txtfile.write("Hasard cryptographiquement sécurisé\n")
            txtfile.write("=" * 50 + "\n\n")

            for partie in parties:
                # Informations de brûlage en en-tête de chaque partie
                premiere_carte = partie.premiere_carte_brulee
                carte_str = f"{premiere_carte.rang}{premiere_carte.couleur}" if premiere_carte else "N/A"

                txtfile.write(f"PARTIE {partie.partie_number}\n")
                txtfile.write(f"BURN: {partie.burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {partie.initial_sync_state}\n")
                txtfile.write("-" * 70 + "\n")
                txtfile.write("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5\n")
                txtfile.write("-" * 55 + "\n")

                for main in partie.mains:
                    # Formater le numéro de manche
                    manche_str = str(main.manche_pb_number) if main.manche_pb_number is not None else "-"

                    # Ligne de données
                    ligne = f"{main.main_number:4d} | {manche_str:6s} | {main.index1_sync_state:6d} | {main.index2_cards_category:8s} | {main.index3_result:6s} | {main.index5_combined}"
                    txtfile.write(ligne + "\n")

                txtfile.write("\n")  # Ligne vide entre parties

        print(f"Export TXT terminé : {filename}")

    def exporter_json(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties en format JSON pour analyse facile

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier JSON
        """
        def carte_to_dict(carte: Carte) -> Dict:
            """Convertit une carte en dictionnaire"""
            return {
                "rang": carte.rang,
                "couleur": carte.couleur,
                "valeur": carte.valeur
            }

        def main_to_dict(main: MainBaccarat) -> Dict:
            """Convertit une main en dictionnaire"""
            return {
                "main_number": main.main_number,
                "manche_pb_number": main.manche_pb_number,
                "cartes_player": [carte_to_dict(c) for c in main.cartes_player],
                "cartes_banker": [carte_to_dict(c) for c in main.cartes_banker],
                "total_cartes_distribuees": main.total_cartes_distribuees,
                "score_player": main.score_player,
                "score_banker": main.score_banker,
                "index1_sync_state": main.index1_sync_state,
                "index2_cards_count": main.index2_cards_count,
                "index2_cards_category": main.index2_cards_category,
                "index3_result": main.index3_result,
                "index5_combined": main.index5_combined,
                "timestamp": main.timestamp
            }

        def partie_to_dict(partie: PartieBaccarat) -> Dict:
            """Convertit une partie en dictionnaire"""
            return {
                "partie_number": partie.partie_number,
                "burn_info": {
                    "cartes_brulees": [carte_to_dict(c) for c in partie.cartes_brulees],
                    "burn_cards_count": partie.burn_cards_count,
                    "burn_parity": partie.burn_parity,
                    "initial_sync_state": partie.initial_sync_state,
                    "premiere_carte_brulee": carte_to_dict(partie.premiere_carte_brulee) if partie.premiere_carte_brulee else None
                },
                "statistiques": {
                    "total_mains": partie.total_mains,
                    "total_manches_pb": partie.total_manches_pb,
                    "total_ties": partie.total_ties,
                    "cut_card_atteinte": partie.cut_card_atteinte,
                    "cartes_restantes": partie.cartes_restantes
                },
                "mains": [main_to_dict(main) for main in partie.mains]
            }

        # Structure JSON principale
        data = {
            "metadata": {
                "generateur": "GÉNÉRATEUR PARTIES BACCARAT LUPASCO",
                "version": "2.0",
                "date_generation": datetime.now().isoformat(),
                "nombre_parties": len(parties),
                "hasard_cryptographique": True,
                "description": "Parties de baccarat générées avec hasard cryptographiquement sécurisé selon les règles Lupasco"
            },
            "configuration": {
                "decks_count": DECKS_COUNT,
                "total_cards": TOTAL_CARDS,
                "cut_card_position": CUT_CARD_POSITION,
                "cards_mapping": CARDS_MAPPING
            },
            "parties": [partie_to_dict(partie) for partie in parties]
        }

        # Écrire le fichier JSON avec indentation pour lisibilité
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, indent=2, ensure_ascii=False)

        print(f"Export JSON terminé : {filename}")

# ============================================================================
# EXEMPLE D'UTILISATION
# ============================================================================

def generer_dataset_complet():
    """Génère 1000 parties complètes pour l'analyse Lupasco avec hasard cryptographiquement sécurisé"""
    print("🎲 GÉNÉRATEUR DE PARTIES BACCARAT LUPASCO")
    print("🔐 AVEC HASARD CRYPTOGRAPHIQUEMENT SÉCURISÉ")
    print("📊 GÉNÉRATION DE 1000 PARTIES COMPLÈTES")
    print("=" * 60)

    # Créer le générateur avec hasard sécurisé
    generateur = GenerateurPartiesBaccarat()

    # Générer 1000 parties complètes (60 manches chacune)
    print("Génération de 1000 parties complètes (60 manches P/B chacune)...")
    print("⚠️  Cette opération peut prendre plusieurs minutes...")
    parties = generateur.generer_multiple_parties(nb_parties=1000000, max_manches=60)

    # Créer les noms de fichiers avec timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename_txt = f"dataset_baccarat_lupasco_{timestamp}.txt"
    filename_json = f"dataset_baccarat_lupasco_{timestamp}.json"

    # Exporter en TXT
    print("📝 Export en cours vers fichier TXT...")
    generateur.exporter_txt(parties, filename_txt)

    # Exporter en JSON
    print("📋 Export en cours vers fichier JSON...")
    generateur.exporter_json(parties, filename_json)

    # Calculer les statistiques détaillées
    print("\n📊 STATISTIQUES DÉTAILLÉES :")
    total_mains = sum(p.total_mains for p in parties)
    total_pb = sum(p.total_manches_pb for p in parties)
    total_ties = sum(p.total_ties for p in parties)

    # Compter les parties complètes (60 manches P/B)
    parties_completes = sum(1 for p in parties if p.total_manches_pb == 60)
    parties_incompletes = len(parties) - parties_completes

    # Compter les résultats
    player_count = 0
    banker_count = 0
    tie_count = 0

    # Compter les cartes
    cards_4_count = 0
    cards_5_count = 0
    cards_6_count = 0

    # Compter les états SYNC/DESYNC
    sync_count = 0
    desync_count = 0

    for partie in parties:
        for main in partie.mains:
            # Plus besoin d'exclure le brûlage car il n'est plus dans les mains
            # Résultats
            if main.index3_result == 'PLAYER':
                player_count += 1
            elif main.index3_result == 'BANKER':
                banker_count += 1
            elif main.index3_result == 'TIE':
                tie_count += 1

            # Cartes
            if main.index2_cards_count == 4:
                cards_4_count += 1
            elif main.index2_cards_count == 5:
                cards_5_count += 1
            elif main.index2_cards_count == 6:
                cards_6_count += 1

            # États SYNC/DESYNC
            if main.index1_sync_state == 0:  # SYNC
                sync_count += 1
            else:  # DESYNC
                desync_count += 1

    print(f"Total parties générées : {len(parties)}")
    print(f"Parties complètes (60 manches P/B) : {parties_completes}")
    print(f"Parties incomplètes : {parties_incompletes}")
    print(f"Total mains générées : {total_mains}")
    print(f"Total manches P/B : {total_pb}")
    print(f"Total TIE : {total_ties}")
    print(f"Taux de réussite : {parties_completes/len(parties)*100:.1f}%")
    print()
    print("RÉPARTITION DES RÉSULTATS :")
    print(f"PLAYER : {player_count} ({player_count/total_mains*100:.2f}%)")
    print(f"BANKER : {banker_count} ({banker_count/total_mains*100:.2f}%)")
    print(f"TIE : {tie_count} ({tie_count/total_mains*100:.2f}%)")
    print()
    print("RÉPARTITION DES CARTES :")
    print(f"4 cartes : {cards_4_count} ({cards_4_count/total_mains*100:.2f}%)")
    print(f"5 cartes : {cards_5_count} ({cards_5_count/total_mains*100:.2f}%)")
    print(f"6 cartes : {cards_6_count} ({cards_6_count/total_mains*100:.2f}%)")
    print()
    print("RÉPARTITION SYNC/DESYNC :")
    print(f"SYNC : {sync_count} ({sync_count/total_mains*100:.2f}%)")
    print(f"DESYNC : {desync_count} ({desync_count/total_mains*100:.2f}%)")

    print(f"\n✅ Dataset généré avec succès !")
    print(f"📝 Fichier TXT : {filename_txt}")
    print(f"📋 Fichier JSON : {filename_json}")
    print(f"💾 1000 parties complètes générées avec hasard cryptographiquement sécurisé")

    return filename_txt, filename_json

def test_generateur():
    """Test simple du générateur pour vérifier la logique et les exports"""
    print("TEST DU GENERATEUR BACCARAT LUPASCO")
    print("=" * 50)

    # Créer le générateur
    generateur = GenerateurPartiesBaccarat(seed=42)  # Seed pour reproductibilité

    # Générer 1 partie complète (60 manches P/B)
    print("Génération d'une partie complète (60 manches P/B)...")
    partie = generateur.generer_partie(1, max_manches=60)

    # Afficher les résultats détaillés
    print(f"\nRESULTATS DE LA PARTIE TEST :")
    print(f"Brûlage : {partie.burn_cards_count} cartes, parité {partie.burn_parity}")
    print(f"État initial : {partie.initial_sync_state}")
    print(f"Total mains : {len(partie.mains)}")
    print(f"Manches P/B : {partie.total_manches_pb}")
    print(f"TIE : {partie.total_ties}")

    # Afficher les informations de brûlage
    premiere_carte = partie.premiere_carte_brulee
    carte_str = f"{premiere_carte.rang}{premiere_carte.couleur}" if premiere_carte else "N/A"
    print(f"BURN: {partie.burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {partie.initial_sync_state}")

    print(f"\nDETAIL DES PREMIERES MAINS :")
    print("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5")
    print("-" * 55)

    for i, main in enumerate(partie.mains[:15]):  # Afficher les 15 premières mains
        manche_str = str(main.manche_pb_number) if main.manche_pb_number is not None else "-"
        print(f"{main.main_number:4d} | {manche_str:6s} | {main.index1_sync_state:6d} | {main.index2_cards_category:8s} | {main.index3_result:6s} | {main.index5_combined}")

    # Test des exports TXT et JSON
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename_txt = f"test_partie_lupasco_{timestamp}.txt"
    filename_json = f"test_partie_lupasco_{timestamp}.json"

    print(f"\nTEST DES EXPORTS :")
    # generateur.exporter_txt([partie], filename_txt)  # DÉSACTIVÉ
    generateur.exporter_json([partie], filename_json)

    print(f"✅ Fichiers de test générés :")
    # print(f"📝 TXT : {filename_txt}")  # DÉSACTIVÉ
    print(f"📋 JSON : {filename_json}")

    return partie

if __name__ == "__main__":
    # Générer 1000 parties complètes
    print("GENERATION DE 1000 PARTIES COMPLETES BACCARAT LUPASCO")
    print("FORMAT TXT ET JSON")
    print("=" * 60)

    # Générer le dataset complet
    filename_txt, filename_json = generer_dataset_complet()

    print(f"\n🎉 GENERATION TERMINEE AVEC SUCCES !")
    print(f"📝 Fichier TXT : {filename_txt}")
    print(f"📋 Fichier JSON : {filename_json}")
    print(f"💾 1000 parties complètes générées avec hasard cryptographiquement sécurisé")
    print(f"🔍 Les fichiers sont prêts pour l'analyse !")
