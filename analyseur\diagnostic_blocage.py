#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnostic de blocage - Identifier où le programme se bloque
"""

import sys
import time

def test_etape(nom, fonction):
    """Test une étape avec timeout"""
    print(f"🔍 Test {nom}...", end=" ", flush=True)
    try:
        debut = time.time()
        resultat = fonction()
        fin = time.time()
        print(f"✅ OK ({fin-debut:.2f}s)")
        return True, resultat
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False, None

def test_imports_basiques():
    """Test des imports de base"""
    import os
    import numpy as np
    import multiprocessing
    return True

def test_psutil():
    """Test psutil"""
    import psutil
    ram = psutil.virtual_memory()
    return ram.available

def test_numba_centralise():
    """Test numba centralisé"""
    from numba_centralise import HAS_NUMBA, afficher_statut_numba
    return HAS_NUMBA

def test_analyseur_import():
    """Test import analyseur"""
    from analyseur_transitions_index5 import AnalyseurEvolutionEntropique
    return True

def test_cache_creation():
    """Test création cache"""
    sys.path.append('.')
    from analyse_complete_avec_diff import CacheRAMGlobal
    cache = CacheRAMGlobal()
    return cache

def test_chargeur_json():
    """Test chargeur JSON"""
    from analyse_complete_avec_diff import ChargeurJSONCentralise
    return ChargeurJSONCentralise.verifier_msgspec()

def main():
    """Diagnostic complet"""
    print("🔍 DIAGNOSTIC DE BLOCAGE")
    print("=" * 50)
    
    # Tests progressifs
    tests = [
        ("Imports basiques", test_imports_basiques),
        ("psutil RAM", test_psutil),
        ("Numba centralisé", test_numba_centralise),
        ("Import analyseur", test_analyseur_import),
        ("Chargeur JSON", test_chargeur_json),
        ("Création cache", test_cache_creation),
    ]
    
    for nom, fonction in tests:
        success, resultat = test_etape(nom, fonction)
        if not success:
            print(f"\n🚨 BLOCAGE IDENTIFIÉ : {nom}")
            print("🔧 Vérifiez cette étape pour résoudre le problème")
            return False
        
        # Pause pour éviter les problèmes de concurrence
        time.sleep(0.1)
    
    print("\n✅ TOUS LES TESTS RÉUSSIS")
    print("🔍 Le blocage vient probablement d'une autre cause")
    
    # Test final : import complet
    print("\n🔍 Test import complet du module principal...")
    try:
        import analyse_complete_avec_diff
        print("✅ Import complet réussi")
        return True
    except Exception as e:
        print(f"❌ ERREUR import complet: {e}")
        return False

if __name__ == "__main__":
    main()
