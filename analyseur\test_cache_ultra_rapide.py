#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du système de cache ultra-rapide
"""

import os
import time

def test_cache_json():
    """Test du cache JSON"""
    print("🔍 TEST CACHE JSON")
    print("=" * 40)
    
    from cache_ultra_rapide import CACHE_ULTRA_RAPIDE
    
    # Afficher les infos du cache
    CACHE_ULTRA_RAPIDE.info_cache()
    
    # Test de chargement JSON avec cache
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    
    if os.path.exists(dataset_path):
        print(f"\n🔍 Test chargement avec cache : {dataset_path}")
        
        def fonction_chargement_test(fichier_path):
            """Fonction de chargement pour test"""
            print("🔄 Chargement JSON de test...")
            import msgspec.json
            
            debut = time.time()
            with open(fichier_path, 'rb') as f:
                data = msgspec.json.decode(f.read())
            fin = time.time()
            
            print(f"✅ JSON chargé en {fin - debut:.2f}s")
            return data
        
        # Premier test : devrait créer le cache
        print("\n📊 PREMIER TEST (création cache attendue)")
        debut_total = time.time()
        data1 = CACHE_ULTRA_RAPIDE.charger_ou_creer_cache_json(dataset_path, fonction_chargement_test)
        fin_total = time.time()
        print(f"⏱️ Temps total premier test : {fin_total - debut_total:.2f}s")
        print(f"📊 Parties chargées : {len(data1.get('parties', []))}")
        
        # Deuxième test : devrait utiliser le cache
        print("\n📊 DEUXIÈME TEST (cache attendu)")
        debut_total = time.time()
        data2 = CACHE_ULTRA_RAPIDE.charger_ou_creer_cache_json(dataset_path, fonction_chargement_test)
        fin_total = time.time()
        print(f"⏱️ Temps total deuxième test : {fin_total - debut_total:.2f}s")
        print(f"📊 Parties chargées : {len(data2.get('parties', []))}")
        
        # Vérifier que les données sont identiques
        if data1 == data2:
            print("✅ Données identiques - Cache fonctionne !")
        else:
            print("❌ Données différentes - Problème de cache")
        
        # Afficher les infos finales du cache
        print("\n📊 INFORMATIONS FINALES DU CACHE")
        CACHE_ULTRA_RAPIDE.info_cache()
        
    else:
        print(f"❌ Fichier {dataset_path} non trouvé")

def test_cache_signatures():
    """Test du cache des signatures"""
    print("\n🔍 TEST CACHE SIGNATURES")
    print("=" * 40)
    
    from cache_ultra_rapide import CACHE_ULTRA_RAPIDE
    
    def generer_signatures_test_l4():
        """Génère des signatures L4 de test"""
        print("🔄 Génération signatures L4 de test...")
        time.sleep(2)  # Simuler le temps de génération
        return {"seq1": 1.0, "seq2": 2.0, "seq3": 3.0}
    
    def generer_signatures_test_l5():
        """Génère des signatures L5 de test"""
        print("🔄 Génération signatures L5 de test...")
        time.sleep(3)  # Simuler le temps de génération
        return {"seq1": 1.5, "seq2": 2.5, "seq3": 3.5, "seq4": 4.5}
    
    # Premier test : devrait générer et cacher
    print("\n📊 PREMIER TEST SIGNATURES (génération + cache attendu)")
    debut = time.time()
    sig4_1, sig5_1 = CACHE_ULTRA_RAPIDE.charger_ou_generer_signatures(
        generer_signatures_test_l4, generer_signatures_test_l5
    )
    fin = time.time()
    print(f"⏱️ Temps premier test : {fin - debut:.2f}s")
    print(f"📊 Signatures L4 : {len(sig4_1)}")
    print(f"📊 Signatures L5 : {len(sig5_1)}")
    
    # Deuxième test : devrait utiliser le cache
    print("\n📊 DEUXIÈME TEST SIGNATURES (cache attendu)")
    debut = time.time()
    sig4_2, sig5_2 = CACHE_ULTRA_RAPIDE.charger_ou_generer_signatures(
        generer_signatures_test_l4, generer_signatures_test_l5
    )
    fin = time.time()
    print(f"⏱️ Temps deuxième test : {fin - debut:.2f}s")
    print(f"📊 Signatures L4 : {len(sig4_2)}")
    print(f"📊 Signatures L5 : {len(sig5_2)}")
    
    # Vérifier que les données sont identiques
    if sig4_1 == sig4_2 and sig5_1 == sig5_2:
        print("✅ Signatures identiques - Cache fonctionne !")
    else:
        print("❌ Signatures différentes - Problème de cache")

def main():
    """Test complet du cache ultra-rapide"""
    print("🚀 TEST SYSTÈME CACHE ULTRA-RAPIDE")
    print("=" * 50)
    
    try:
        # Test cache JSON
        test_cache_json()
        
        # Test cache signatures
        test_cache_signatures()
        
        print("\n✅ TESTS TERMINÉS")
        
    except Exception as e:
        print(f"❌ ERREUR DURANT LES TESTS : {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
