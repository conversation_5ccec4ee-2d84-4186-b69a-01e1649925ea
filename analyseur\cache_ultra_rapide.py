#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SYSTÈME DE CACHE ULTRA-RAPIDE
Réduction du temps de lancement de 10+ minutes à 15-20 secondes
"""

import os
import pickle
import time
import hashlib
import numpy as np
from pathlib import Path

class CacheUltraRapide:
    """
    Système de cache ultra-rapide pour :
    1. Cache JSON (38s → 2-3s)
    2. Cache signatures L4/L5 (5-10min → 5-10s)
    3. Cache résultats d'analyse
    
    Gain total attendu : 50-100x plus rapide au lancement
    """
    
    def __init__(self, cache_dir="cache_ultra"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Fichiers de cache
        self.cache_json = self.cache_dir / "dataset_json.pkl"
        self.cache_signatures_l4 = self.cache_dir / "signatures_l4.pkl"
        self.cache_signatures_l5 = self.cache_dir / "signatures_l5.pkl"
        self.cache_metadata = self.cache_dir / "metadata.pkl"
        
        print(f"🚀 Cache ultra-rapide initialisé : {self.cache_dir}")
    
    def calculer_hash_fichier(self, fichier_path):
        """Calcule le hash d'un fichier pour détecter les modifications"""
        if not os.path.exists(fichier_path):
            return None
        
        hash_md5 = hashlib.md5()
        with open(fichier_path, "rb") as f:
            # Lire par chunks pour les gros fichiers
            for chunk in iter(lambda: f.read(8192), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def cache_json_valide(self, fichier_json_path):
        """Vérifie si le cache JSON est valide (fichier non modifié)"""
        if not self.cache_json.exists() or not self.cache_metadata.exists():
            return False
        
        # Charger les métadonnées du cache
        try:
            with open(self.cache_metadata, 'rb') as f:
                metadata = pickle.load(f)
            
            # Vérifier le hash du fichier JSON
            hash_actuel = self.calculer_hash_fichier(fichier_json_path)
            hash_cache = metadata.get('json_hash')
            
            return hash_actuel == hash_cache
        except:
            return False
    
    def sauvegarder_cache_json(self, data, fichier_json_path):
        """Sauvegarde les données JSON en cache"""
        print("💾 Sauvegarde cache JSON...")
        debut = time.time()
        
        # Sauvegarder les données
        with open(self.cache_json, 'wb') as f:
            pickle.dump(data, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        # Sauvegarder les métadonnées
        metadata = {
            'json_hash': self.calculer_hash_fichier(fichier_json_path),
            'timestamp': time.time(),
            'nb_parties': len(data.get('parties', [])),
            'taille_fichier': os.path.getsize(fichier_json_path)
        }
        
        with open(self.cache_metadata, 'wb') as f:
            pickle.dump(metadata, f)
        
        fin = time.time()
        print(f"✅ Cache JSON sauvegardé en {fin - debut:.2f}s")
        print(f"   📁 Fichier cache : {self.cache_json}")
        print(f"   📊 Taille cache : {os.path.getsize(self.cache_json) / (1024**2):.1f}MB")
    
    def charger_cache_json(self):
        """Charge les données JSON depuis le cache"""
        print("🚀 Chargement cache JSON...")
        debut = time.time()
        
        with open(self.cache_json, 'rb') as f:
            data = pickle.load(f)
        
        fin = time.time()
        print(f"✅ Cache JSON chargé en {fin - debut:.2f}s")
        print(f"   📊 Parties chargées : {len(data.get('parties', [])):,}")
        print(f"   ⚡ Gain vs JSON : ~15-20x plus rapide")
        
        return data
    
    def charger_ou_creer_cache_json(self, fichier_json_path, fonction_chargement):
        """
        Charge le cache JSON s'il existe et est valide,
        sinon charge le JSON et crée le cache
        """
        print("🔍 Vérification cache JSON...")
        
        if self.cache_json_valide(fichier_json_path):
            print("✅ Cache JSON valide trouvé")
            return self.charger_cache_json()
        else:
            print("❌ Cache JSON invalide ou inexistant")
            print("🔄 Chargement JSON et création du cache...")
            
            # Charger le JSON avec la fonction fournie
            data = fonction_chargement(fichier_json_path)
            
            # Sauvegarder en cache pour les prochaines fois
            self.sauvegarder_cache_json(data, fichier_json_path)
            
            return data
    
    def sauvegarder_cache_signatures(self, signatures_l4, signatures_l5):
        """Sauvegarde les signatures L4/L5 en cache"""
        print("💾 Sauvegarde cache signatures...")
        debut = time.time()
        
        # Sauvegarder L4
        with open(self.cache_signatures_l4, 'wb') as f:
            pickle.dump(signatures_l4, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        # Sauvegarder L5
        with open(self.cache_signatures_l5, 'wb') as f:
            pickle.dump(signatures_l5, f, protocol=pickle.HIGHEST_PROTOCOL)
        
        fin = time.time()
        print(f"✅ Cache signatures sauvegardé en {fin - debut:.2f}s")
        print(f"   📊 Signatures L4 : {len(signatures_l4):,}")
        print(f"   📊 Signatures L5 : {len(signatures_l5):,}")
    
    def charger_cache_signatures(self):
        """Charge les signatures L4/L5 depuis le cache"""
        if not (self.cache_signatures_l4.exists() and self.cache_signatures_l5.exists()):
            return None, None
        
        print("🚀 Chargement cache signatures...")
        debut = time.time()
        
        # Charger L4
        with open(self.cache_signatures_l4, 'rb') as f:
            signatures_l4 = pickle.load(f)
        
        # Charger L5
        with open(self.cache_signatures_l5, 'rb') as f:
            signatures_l5 = pickle.load(f)
        
        fin = time.time()
        print(f"✅ Cache signatures chargé en {fin - debut:.2f}s")
        print(f"   📊 Signatures L4 : {len(signatures_l4):,}")
        print(f"   📊 Signatures L5 : {len(signatures_l5):,}")
        print(f"   ⚡ Gain vs génération : ~50-100x plus rapide")
        
        return signatures_l4, signatures_l5
    
    def charger_ou_generer_signatures(self, fonction_generation_l4, fonction_generation_l5):
        """
        Charge les signatures depuis le cache s'il existe,
        sinon les génère et les met en cache
        """
        print("🔍 Vérification cache signatures...")
        
        signatures_l4, signatures_l5 = self.charger_cache_signatures()
        
        if signatures_l4 is not None and signatures_l5 is not None:
            print("✅ Cache signatures valide trouvé")
            return signatures_l4, signatures_l5
        else:
            print("❌ Cache signatures inexistant")
            print("🔄 Génération signatures et création du cache...")
            
            # Générer les signatures
            signatures_l4 = fonction_generation_l4()
            signatures_l5 = fonction_generation_l5()
            
            # Sauvegarder en cache pour les prochaines fois
            self.sauvegarder_cache_signatures(signatures_l4, signatures_l5)
            
            return signatures_l4, signatures_l5
    
    def nettoyer_cache(self):
        """Nettoie tous les fichiers de cache"""
        print("🧹 Nettoyage du cache...")
        
        fichiers_supprimes = 0
        for fichier in self.cache_dir.glob("*.pkl"):
            fichier.unlink()
            fichiers_supprimes += 1
        
        print(f"✅ Cache nettoyé : {fichiers_supprimes} fichiers supprimés")
    
    def info_cache(self):
        """Affiche les informations sur le cache"""
        print("📊 INFORMATIONS CACHE")
        print("=" * 40)
        
        # Cache JSON
        if self.cache_json.exists():
            taille_mb = os.path.getsize(self.cache_json) / (1024**2)
            print(f"✅ Cache JSON : {taille_mb:.1f}MB")
        else:
            print("❌ Cache JSON : Inexistant")
        
        # Cache signatures
        if self.cache_signatures_l4.exists() and self.cache_signatures_l5.exists():
            taille_l4 = os.path.getsize(self.cache_signatures_l4) / (1024**2)
            taille_l5 = os.path.getsize(self.cache_signatures_l5) / (1024**2)
            print(f"✅ Cache signatures L4 : {taille_l4:.1f}MB")
            print(f"✅ Cache signatures L5 : {taille_l5:.1f}MB")
        else:
            print("❌ Cache signatures : Inexistant")
        
        # Métadonnées
        if self.cache_metadata.exists():
            with open(self.cache_metadata, 'rb') as f:
                metadata = pickle.load(f)
            print(f"📊 Dernière mise à jour : {time.ctime(metadata['timestamp'])}")
            print(f"📊 Parties en cache : {metadata['nb_parties']:,}")
        
        print("=" * 40)


# Instance globale du cache
CACHE_ULTRA_RAPIDE = CacheUltraRapide()
