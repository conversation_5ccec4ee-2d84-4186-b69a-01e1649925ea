#!/usr/bin/env python3
"""
MODULE SIGNATURES ENTROPIQUES BCT
=================================

Module spécialisé pour générer et analyser les signatures entropiques
de TOUTES les séquences possibles de longueur 4 et 5 selon les règles BCT.

OBJECTIF : Créer un catalogue exhaustif des signatures entropiques pour
éliminer définitivement le moyennage statistique.

RÈGLES BCT STRICTES :
- INDEX2 = C → INDEX1 alterne (0↔1)  
- INDEX2 = A,B → INDEX1 conservé (0→0, 1→1)

Auteur : Système BCT - Module Signatures Entropiques
Date : 2025-01-22
"""

import json
import numpy as np
import pickle
import os
from collections import Counter, defaultdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Set
import itertools


class GenerateurSignaturesEntropiques:
    """
    Générateur exhaustif des signatures entropiques pour toutes les séquences
    possibles de longueur 4 et 5 selon les règles BCT
    """
    
    def __init__(self):
        """Initialise le générateur de signatures entropiques"""
        
        # Composants INDEX5
        self.index1_values = ['0', '1']
        self.index2_values = ['A', 'B', 'C']
        self.index3_values = ['BANKER', 'PLAYER', 'TIE']
        
        # Règles de transition INDEX1 selon INDEX2
        self.regles_transition = {
            'C': 'ALTERNANCE',    # C → alternance 0↔1
            'A': 'CONSERVATION',  # A → conservation 0→0, 1→1
            'B': 'CONSERVATION'   # B → conservation 0→0, 1→1
        }
        
        # Toutes les valeurs INDEX5 possibles (18 valeurs)
        self.valeurs_index5 = self._generer_toutes_valeurs_index5()
        
        # Cache pour les signatures
        self.cache_dir = Path("cache_signatures_entropiques")
        self.cache_dir.mkdir(exist_ok=True)
        
        # Stockage des signatures
        self.signatures_longueur_4 = {}
        self.signatures_longueur_5 = {}
        self.catalogue_distributions = {}
        
        print("🎯 GÉNÉRATEUR SIGNATURES ENTROPIQUES INITIALISÉ")
        print("=" * 50)
        print(f"✅ {len(self.valeurs_index5)} valeurs INDEX5 configurées")
        print("✅ Règles BCT strictes intégrées")
        print("✅ Cache de signatures activé")
        
    def _generer_toutes_valeurs_index5(self) -> List[str]:
        """Génère toutes les 18 valeurs INDEX5 possibles"""
        
        valeurs = []
        for i1 in self.index1_values:
            for i2 in self.index2_values:
                for i3 in self.index3_values:
                    valeurs.append(f"{i1}_{i2}_{i3}")
        
        return valeurs
    
    def calculer_index1_suivant(self, index1_actuel: str, index2_actuel: str) -> str:
        """
        Calcule INDEX1 suivant selon les règles BCT strictes
        
        Args:
            index1_actuel: '0' ou '1'
            index2_actuel: 'A', 'B', ou 'C'
            
        Returns:
            str: INDEX1 suivant selon les règles déterministes
        """
        
        if index2_actuel == 'C':
            # Alternance : 0→1, 1→0
            return '1' if index1_actuel == '0' else '0'
        else:  # A ou B
            # Conservation : 0→0, 1→1
            return index1_actuel
    
    def generer_transitions_valides(self, valeur_index5: str) -> List[str]:
        """
        Génère toutes les transitions INDEX5 valides depuis une valeur source
        selon les règles BCT strictes
        
        Args:
            valeur_index5: Valeur INDEX5 source (ex: '0_A_BANKER')
            
        Returns:
            List[str]: Liste des valeurs INDEX5 cibles valides
        """
        
        # Parser la valeur source
        parts = valeur_index5.split('_')
        if len(parts) != 3:
            return []
            
        index1_source, index2_source, index3_source = parts
        
        # Calculer INDEX1 suivant selon les règles BCT
        index1_suivant = self.calculer_index1_suivant(index1_source, index2_source)
        
        # Générer toutes les combinaisons valides avec ce INDEX1
        transitions_valides = []
        for index2_cible in self.index2_values:
            for index3_cible in self.index3_values:
                valeur_cible = f"{index1_suivant}_{index2_cible}_{index3_cible}"
                transitions_valides.append(valeur_cible)
                
        return transitions_valides
    
    def calculer_signature_entropique(self, sequence: Tuple[str, ...]) -> Dict:
        """
        Calcule la signature entropique complète d'une séquence
        
        Args:
            sequence: Tuple de valeurs INDEX5
            
        Returns:
            Dict: Signature entropique complète
        """
        
        # Compter les occurrences de chaque valeur
        counts = Counter(sequence)
        total = len(sequence)
        
        # Calculer les probabilités
        probabilities = [count/total for count in counts.values()]
        
        # Calculer l'entropie de Shannon
        entropie_shannon = -sum(p * np.log2(p) for p in probabilities if p > 0)
        
        # Distribution pattern (tri décroissant des occurrences)
        distribution_pattern = tuple(sorted(counts.values(), reverse=True))
        
        # Métriques complémentaires
        nb_valeurs_distinctes = len(counts)
        dominance_ratio = max(probabilities) / min(probabilities) if len(probabilities) > 1 else float('inf')
        uniformite_score = 1 - np.std(probabilities) if len(probabilities) > 1 else 1.0
        
        # Classification prédictive
        classe_predictive = self._classifier_par_entropie(entropie_shannon, total)
        
        # Potentiel prédictif (0-100)
        entropie_max = np.log2(min(total, 18))  # Maximum théorique
        potentiel_predictif = (1 - entropie_shannon/entropie_max) * 100 if entropie_max > 0 else 0
        
        return {
            'sequence': sequence,
            'longueur': total,
            'entropie_shannon': round(entropie_shannon, 4),
            'distribution_pattern': distribution_pattern,
            'nb_valeurs_distinctes': nb_valeurs_distinctes,
            'dominance_ratio': round(dominance_ratio, 3) if dominance_ratio != float('inf') else 'INF',
            'uniformite_score': round(uniformite_score, 3),
            'classe_predictive': classe_predictive,
            'potentiel_predictif': round(potentiel_predictif, 1),
            'valeurs_uniques': list(counts.keys()),
            'occurrences': dict(counts)
        }
    
    def _classifier_par_entropie(self, entropie: float, longueur: int) -> str:
        """Classification prédictive basée sur l'entropie"""

        if entropie == 0:
            return "DÉTERMINISTE"
        elif entropie < 0.8:
            return "TRÈS_PRÉDICTIBLE"
        elif entropie < 1.3:
            return "MODÉRÉMENT_PRÉDICTIBLE"
        elif entropie < 1.8:
            return "PEU_PRÉDICTIBLE"
        else:
            return "QUASI_ALÉATOIRE"

    def generer_toutes_sequences_longueur_4(self) -> Dict:
        """
        Génère TOUTES les séquences possibles de longueur 4 selon les règles BCT
        et calcule leur signature entropique

        Returns:
            Dict: Catalogue complet des séquences longueur 4 avec signatures
        """

        print("\n🔥 GÉNÉRATION EXHAUSTIVE - SÉQUENCES LONGUEUR 4")
        print("=" * 60)
        print("📊 Respect strict des règles BCT INDEX1/INDEX2")

        # Vérifier le cache
        cache_file = self.cache_dir / "signatures_longueur_4.pkl"
        if cache_file.exists():
            print("📁 Cache trouvé, chargement...")
            with open(cache_file, 'rb') as f:
                self.signatures_longueur_4 = pickle.load(f)
            print(f"✅ {len(self.signatures_longueur_4):,} signatures chargées du cache")
            return self.signatures_longueur_4

        sequences_generees = set()

        # Générer toutes les séquences de longueur 4
        for val1 in self.valeurs_index5:
            transitions_val1 = self.generer_transitions_valides(val1)

            for val2 in transitions_val1:
                transitions_val2 = self.generer_transitions_valides(val2)

                for val3 in transitions_val2:
                    transitions_val3 = self.generer_transitions_valides(val3)

                    for val4 in transitions_val3:
                        sequence = (val1, val2, val3, val4)
                        sequences_generees.add(sequence)

        print(f"🎯 {len(sequences_generees):,} séquences uniques générées")

        # Calculer les signatures entropiques
        print("🔬 Calcul des signatures entropiques...")

        for i, sequence in enumerate(sequences_generees):
            signature = self.calculer_signature_entropique(sequence)
            self.signatures_longueur_4[sequence] = signature

            if (i + 1) % 1000 == 0:
                print(f"   Progression : {i + 1:,}/{len(sequences_generees):,} signatures calculées")

        # Sauvegarder dans le cache
        with open(cache_file, 'wb') as f:
            pickle.dump(self.signatures_longueur_4, f)

        print(f"✅ {len(self.signatures_longueur_4):,} signatures longueur 4 générées et sauvegardées")

        return self.signatures_longueur_4

    def generer_toutes_sequences_longueur_5(self) -> Dict:
        """
        Génère TOUTES les séquences possibles de longueur 5 selon les règles BCT
        et calcule leur signature entropique

        Returns:
            Dict: Catalogue complet des séquences longueur 5 avec signatures
        """

        print("\n🔥 GÉNÉRATION EXHAUSTIVE - SÉQUENCES LONGUEUR 5")
        print("=" * 60)
        print("📊 Respect strict des règles BCT INDEX1/INDEX2")

        # Vérifier le cache
        cache_file = self.cache_dir / "signatures_longueur_5.pkl"
        if cache_file.exists():
            print("📁 Cache trouvé, chargement...")
            with open(cache_file, 'rb') as f:
                self.signatures_longueur_5 = pickle.load(f)
            print(f"✅ {len(self.signatures_longueur_5):,} signatures chargées du cache")
            return self.signatures_longueur_5

        sequences_generees = set()

        # Générer toutes les séquences de longueur 5
        for val1 in self.valeurs_index5:
            transitions_val1 = self.generer_transitions_valides(val1)

            for val2 in transitions_val1:
                transitions_val2 = self.generer_transitions_valides(val2)

                for val3 in transitions_val2:
                    transitions_val3 = self.generer_transitions_valides(val3)

                    for val4 in transitions_val3:
                        transitions_val4 = self.generer_transitions_valides(val4)

                        for val5 in transitions_val4:
                            sequence = (val1, val2, val3, val4, val5)
                            sequences_generees.add(sequence)

        print(f"🎯 {len(sequences_generees):,} séquences uniques générées")

        # Calculer les signatures entropiques
        print("🔬 Calcul des signatures entropiques...")

        for i, sequence in enumerate(sequences_generees):
            signature = self.calculer_signature_entropique(sequence)
            self.signatures_longueur_5[sequence] = signature

            if (i + 1) % 5000 == 0:
                print(f"   Progression : {i + 1:,}/{len(sequences_generees):,} signatures calculées")

        # Sauvegarder dans le cache
        with open(cache_file, 'wb') as f:
            pickle.dump(self.signatures_longueur_5, f)

        print(f"✅ {len(self.signatures_longueur_5):,} signatures longueur 5 générées et sauvegardées")

        return self.signatures_longueur_5

    def analyser_catalogue_signatures(self) -> Dict:
        """
        Analyse complète du catalogue des signatures entropiques

        Returns:
            Dict: Rapport d'analyse complet
        """

        print("\n📊 ANALYSE DU CATALOGUE DES SIGNATURES ENTROPIQUES")
        print("=" * 60)

        # Générer les catalogues si nécessaire
        if not self.signatures_longueur_4:
            self.generer_toutes_sequences_longueur_4()
        if not self.signatures_longueur_5:
            self.generer_toutes_sequences_longueur_5()

        rapport = {
            'longueur_4': self._analyser_signatures_par_longueur(self.signatures_longueur_4, 4),
            'longueur_5': self._analyser_signatures_par_longueur(self.signatures_longueur_5, 5),
            'comparaison_longueurs': {},
            'recommandations_predictives': {}
        }

        # Comparaison entre longueurs
        rapport['comparaison_longueurs'] = {
            'nb_sequences_4': len(self.signatures_longueur_4),
            'nb_sequences_5': len(self.signatures_longueur_5),
            'ratio_sequences': len(self.signatures_longueur_5) / len(self.signatures_longueur_4) if len(self.signatures_longueur_4) > 0 else 0,
            'entropie_moyenne_4': np.mean([sig['entropie_shannon'] for sig in self.signatures_longueur_4.values()]),
            'entropie_moyenne_5': np.mean([sig['entropie_shannon'] for sig in self.signatures_longueur_5.values()])
        }

        # Recommandations prédictives
        rapport['recommandations_predictives'] = self._generer_recommandations_predictives()

        return rapport

    def _analyser_signatures_par_longueur(self, signatures: Dict, longueur: int) -> Dict:
        """Analyse détaillée des signatures pour une longueur donnée"""

        # Grouper par classe prédictive
        par_classe = defaultdict(list)
        par_distribution = defaultdict(list)
        par_entropie = defaultdict(list)

        for sequence, signature in signatures.items():
            par_classe[signature['classe_predictive']].append(signature)
            par_distribution[signature['distribution_pattern']].append(signature)

            # Grouper par tranche d'entropie
            entropie_tranche = round(signature['entropie_shannon'], 1)
            par_entropie[entropie_tranche].append(signature)

        # Statistiques globales
        entropies = [sig['entropie_shannon'] for sig in signatures.values()]
        potentiels = [sig['potentiel_predictif'] for sig in signatures.values()]

        return {
            'nb_sequences_total': len(signatures),
            'statistiques_entropie': {
                'moyenne': round(np.mean(entropies), 4),
                'mediane': round(np.median(entropies), 4),
                'ecart_type': round(np.std(entropies), 4),
                'minimum': round(min(entropies), 4),
                'maximum': round(max(entropies), 4)
            },
            'statistiques_potentiel': {
                'moyenne': round(np.mean(potentiels), 1),
                'mediane': round(np.median(potentiels), 1),
                'ecart_type': round(np.std(potentiels), 1),
                'minimum': round(min(potentiels), 1),
                'maximum': round(max(potentiels), 1)
            },
            'repartition_par_classe': {
                classe: len(sequences) for classe, sequences in par_classe.items()
            },
            'repartition_par_distribution': {
                str(pattern): len(sequences) for pattern, sequences in par_distribution.items()
            },
            'sequences_tres_predictibles': [
                sig for sig in signatures.values()
                if sig['classe_predictive'] in ['DÉTERMINISTE', 'TRÈS_PRÉDICTIBLE']
            ]
        }

    def _generer_recommandations_predictives(self) -> Dict:
        """Génère des recommandations pour l'utilisation prédictive"""

        # Identifier les meilleures signatures
        meilleures_4 = sorted(
            self.signatures_longueur_4.values(),
            key=lambda x: x['potentiel_predictif'],
            reverse=True
        )[:100]  # Top 100

        meilleures_5 = sorted(
            self.signatures_longueur_5.values(),
            key=lambda x: x['potentiel_predictif'],
            reverse=True
        )[:100]  # Top 100

        return {
            'top_100_longueur_4': [
                {
                    'sequence': sig['sequence'],
                    'entropie': sig['entropie_shannon'],
                    'potentiel': sig['potentiel_predictif'],
                    'classe': sig['classe_predictive']
                }
                for sig in meilleures_4
            ],
            'top_100_longueur_5': [
                {
                    'sequence': sig['sequence'],
                    'entropie': sig['entropie_shannon'],
                    'potentiel': sig['potentiel_predictif'],
                    'classe': sig['classe_predictive']
                }
                for sig in meilleures_5
            ],
            'seuils_recommandes': {
                'entropie_max_predictible': 1.0,
                'potentiel_min_utilisation': 60.0,
                'nb_valeurs_max_optimales': 3
            }
        }

    def exporter_catalogue_complet(self, format_export: str = 'json') -> str:
        """
        Exporte le catalogue complet des signatures entropiques

        Args:
            format_export: 'json', 'txt', ou 'csv'

        Returns:
            str: Chemin du fichier exporté
        """

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format_export == 'json':
            return self._exporter_json(timestamp)
        elif format_export == 'txt':
            return self._exporter_txt(timestamp)
        elif format_export == 'csv':
            return self._exporter_csv(timestamp)
        else:
            raise ValueError(f"Format d'export non supporté : {format_export}")

    def _exporter_json(self, timestamp: str) -> str:
        """Export au format JSON"""

        filename = f"catalogue_signatures_entropiques_{timestamp}.json"
        filepath = self.cache_dir / filename

        catalogue_complet = {
            'metadata': {
                'date_generation': timestamp,
                'nb_sequences_4': len(self.signatures_longueur_4),
                'nb_sequences_5': len(self.signatures_longueur_5),
                'regles_bct': self.regles_transition
            },
            'signatures_longueur_4': {
                str(seq): sig for seq, sig in self.signatures_longueur_4.items()
            },
            'signatures_longueur_5': {
                str(seq): sig for seq, sig in self.signatures_longueur_5.items()
            }
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(catalogue_complet, f, indent=2, ensure_ascii=False)

        print(f"✅ Catalogue exporté : {filepath}")
        return str(filepath)

    def _exporter_txt(self, timestamp: str) -> str:
        """Export au format TXT lisible"""

        filename = f"rapport_signatures_entropiques_{timestamp}.txt"
        filepath = self.cache_dir / filename

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("CATALOGUE DES SIGNATURES ENTROPIQUES BCT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date de génération : {timestamp}\n")
            f.write(f"Séquences longueur 4 : {len(self.signatures_longueur_4):,}\n")
            f.write(f"Séquences longueur 5 : {len(self.signatures_longueur_5):,}\n\n")

            # Top séquences par potentiel prédictif
            f.write("TOP 50 SÉQUENCES LONGUEUR 4 (par potentiel prédictif)\n")
            f.write("-" * 50 + "\n")

            top_4 = sorted(
                self.signatures_longueur_4.values(),
                key=lambda x: x['potentiel_predictif'],
                reverse=True
            )[:50]

            for i, sig in enumerate(top_4, 1):
                f.write(f"{i:2d}. {sig['sequence']}\n")
                f.write(f"    Entropie: {sig['entropie_shannon']:.4f} | ")
                f.write(f"Potentiel: {sig['potentiel_predictif']:.1f}% | ")
                f.write(f"Classe: {sig['classe_predictive']}\n\n")

            f.write("\nTOP 50 SÉQUENCES LONGUEUR 5 (par potentiel prédictif)\n")
            f.write("-" * 50 + "\n")

            top_5 = sorted(
                self.signatures_longueur_5.values(),
                key=lambda x: x['potentiel_predictif'],
                reverse=True
            )[:50]

            for i, sig in enumerate(top_5, 1):
                f.write(f"{i:2d}. {sig['sequence']}\n")
                f.write(f"    Entropie: {sig['entropie_shannon']:.4f} | ")
                f.write(f"Potentiel: {sig['potentiel_predictif']:.1f}% | ")
                f.write(f"Classe: {sig['classe_predictive']}\n\n")

        print(f"✅ Rapport exporté : {filepath}")
        return str(filepath)

    def rechercher_signature(self, sequence: Tuple[str, ...]) -> Dict:
        """
        Recherche la signature d'une séquence spécifique

        Args:
            sequence: Séquence à rechercher

        Returns:
            Dict: Signature trouvée ou None
        """

        longueur = len(sequence)

        if longueur == 4:
            return self.signatures_longueur_4.get(sequence)
        elif longueur == 5:
            return self.signatures_longueur_5.get(sequence)
        else:
            return None

    def filtrer_par_potentiel(self, potentiel_min: float = 60.0, longueur: int = None) -> Dict:
        """
        Filtre les séquences par potentiel prédictif minimum

        Args:
            potentiel_min: Potentiel minimum requis
            longueur: 4, 5, ou None pour toutes

        Returns:
            Dict: Séquences filtrées
        """

        sequences_filtrees = {}

        if longueur is None or longueur == 4:
            for seq, sig in self.signatures_longueur_4.items():
                if sig['potentiel_predictif'] >= potentiel_min:
                    sequences_filtrees[seq] = sig

        if longueur is None or longueur == 5:
            for seq, sig in self.signatures_longueur_5.items():
                if sig['potentiel_predictif'] >= potentiel_min:
                    sequences_filtrees[seq] = sig

        return sequences_filtrees


def main():
    """Fonction principale pour tester le module"""

    print("🎯 TEST DU MODULE SIGNATURES ENTROPIQUES")
    print("=" * 50)

    # Créer le générateur
    generateur = GenerateurSignaturesEntropiques()

    # Générer les catalogues
    signatures_4 = generateur.generer_toutes_sequences_longueur_4()
    signatures_5 = generateur.generer_toutes_sequences_longueur_5()

    # Analyser
    rapport = generateur.analyser_catalogue_signatures()

    # Exporter
    fichier_json = generateur.exporter_catalogue_complet('json')
    fichier_txt = generateur.exporter_catalogue_complet('txt')

    print(f"\n📊 RÉSULTATS :")
    print(f"Séquences longueur 4 : {len(signatures_4):,}")
    print(f"Séquences longueur 5 : {len(signatures_5):,}")
    print(f"Fichiers exportés : {fichier_json}, {fichier_txt}")


if __name__ == "__main__":
    main()
