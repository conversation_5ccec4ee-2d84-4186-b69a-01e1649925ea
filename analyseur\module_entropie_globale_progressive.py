#!/usr/bin/env python3
"""
MODULE ENTROPIE GLOBALE PROGRESSIVE BCT
======================================

Module spécialisé pour calculer l'entropie globale progressive de chaque main
dans une partie, depuis la main 1 jusqu'à la main n.

OBJECTIF : Fournir les signatures entropiques globales nécessaires pour
calculer les ratios de désordre et prédire les tendances entropiques.

FONCTIONNEMENT :
- Main 1 : Entropie de [Main1]
- Main 2 : Entropie de [Main1, Main2]  
- Main 3 : Entropie de [Main1, Main2, Main3]
- Main n : Entropie de [Main1, Main2, ..., Main_n]

Auteur : Système BCT - Module Entropie Globale Progressive
Date : 2025-01-22
"""

import json
import numpy as np
import pickle
import os
from collections import Counter, defaultdict
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any
import time


class CalculateurEntropieGlobaleProgressive:
    """
    Calculateur d'entropie globale progressive pour les parties BCT
    
    Calcule l'entropie de la séquence globale à chaque position de main,
    permettant l'analyse des ratios de désordre local/global.
    """
    
    def __init__(self):
        """Initialise le calculateur d'entropie globale progressive"""
        
        # Cache pour optimisation
        self.cache_dir = Path("cache_entropie_globale")
        self.cache_dir.mkdir(exist_ok=True)
        
        # Stockage des résultats
        self.entropies_parties = {}
        
        # Statistiques de performance
        self.stats_calcul = {
            'parties_traitees': 0,
            'temps_total': 0.0,
            'temps_moyen_par_partie': 0.0
        }
        
        print("🎯 CALCULATEUR ENTROPIE GLOBALE PROGRESSIVE INITIALISÉ")
        print("=" * 55)
        print("✅ Calcul progressif main par main")
        print("✅ Optimisation incrémentale activée")
        print("✅ Cache de performance configuré")
        
    def calculer_entropie_shannon(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie de Shannon d'une séquence
        
        Args:
            sequence: Liste de valeurs INDEX5
            
        Returns:
            float: Entropie de Shannon en bits
        """
        
        if not sequence:
            return 0.0
        
        # Compter les occurrences
        counts = Counter(sequence)
        total = len(sequence)
        
        # Calculer les probabilités
        probabilities = [count/total for count in counts.values()]
        
        # Calculer l'entropie de Shannon
        entropie = -sum(p * np.log2(p) for p in probabilities if p > 0)
        
        return entropie
    
    def calculer_entropies_globales_partie(self, partie: Dict) -> Dict:
        """
        Calcule les entropies globales progressives pour une partie complète
        
        Args:
            partie: Dictionnaire contenant les données d'une partie
            
        Returns:
            Dict: Entropies globales pour chaque position de main
        """
        
        start_time = time.time()
        
        partie_id = partie.get('partie_number', 'unknown')
        mains = partie.get('mains', [])
        
        if not mains:
            return {'erreur': 'Aucune main trouvée dans la partie'}
        
        print(f"🔄 Calcul entropies globales - Partie {partie_id} ({len(mains)} mains)")
        
        # Extraire la séquence INDEX5 complète
        sequence_complete = [main.get('index5_combined', '') for main in mains]
        
        # Vérifier la validité des données
        if not all(sequence_complete):
            return {'erreur': 'Données INDEX5 manquantes ou invalides'}
        
        # Calcul progressif des entropies globales
        entropies_globales = {}
        
        for position in range(1, len(sequence_complete) + 1):
            # Séquence globale depuis le début jusqu'à la position courante
            sequence_globale = sequence_complete[0:position]
            
            # Calculer l'entropie de cette séquence globale
            entropie_globale = self.calculer_entropie_shannon(sequence_globale)
            
            # Analyser la distribution
            counts = Counter(sequence_globale)
            distribution_pattern = tuple(sorted(counts.values(), reverse=True))
            
            # Calculer des métriques complémentaires - CORRECTION STATISTIQUE RIGOUREUSE
            nb_valeurs_distinctes = len(counts)

            # CORRECTION : Entropie maximale théorique pour 18 valeurs INDEX5 possibles
            entropie_max_theorique = np.log2(18)  # 4.1699 bits - Maximum théorique absolu
            entropie_max_observee = np.log2(nb_valeurs_distinctes) if nb_valeurs_distinctes > 1 else 0.0

            # Pourcentages par rapport aux deux références statistiques
            pourcentage_entropie_max = (entropie_globale / entropie_max_theorique * 100) if entropie_max_theorique > 0 else 0
            pourcentage_entropie_observee = (entropie_globale / entropie_max_observee * 100) if entropie_max_observee > 0 else 0
            
            # Stocker les résultats avec métriques statistiques complètes
            entropies_globales[position] = {
                'entropie_globale': round(entropie_globale, 4),
                'longueur_sequence': position,
                'nb_valeurs_distinctes': nb_valeurs_distinctes,
                'distribution_pattern': distribution_pattern,
                'entropie_max_theorique': round(entropie_max_theorique, 4),  # log₂(18) = 4.1699
                'entropie_max_observee': round(entropie_max_observee, 4),
                'pourcentage_entropie_max': round(pourcentage_entropie_max, 1),  # % vs théorique absolu
                'pourcentage_entropie_observee': round(pourcentage_entropie_observee, 1),  # % vs observé
                'sequence_globale': sequence_globale.copy()  # Pour debug si nécessaire
            }
            
            # Affichage de progression pour les longues parties
            if position % 20 == 0:
                print(f"   Position {position}/{len(sequence_complete)} - Entropie: {entropie_globale:.4f}")
        
        # Analyser la convergence
        entropie_finale = entropies_globales[len(sequence_complete)]['entropie_globale']
        entropie_theorique_limite = np.log2(18)  # Limite théorique pour 18 valeurs INDEX5
        convergence_vers_limite = (entropie_finale / entropie_theorique_limite) > 0.85
        
        # Calculer la tendance d'évolution
        if len(entropies_globales) >= 10:
            entropies_fin = [entropies_globales[i]['entropie_globale'] for i in range(len(entropies_globales)-9, len(entropies_globales)+1)]
            tendance_finale = np.polyfit(range(len(entropies_fin)), entropies_fin, 1)[0]
        else:
            tendance_finale = 0.0
        
        # Temps de calcul
        temps_calcul = time.time() - start_time
        
        # Résultat complet
        resultat = {
            'partie_id': partie_id,
            'nb_mains_total': len(mains),
            'entropies_globales': entropies_globales,
            'analyse_convergence': {
                'entropie_finale': entropie_finale,
                'entropie_theorique_limite': round(entropie_theorique_limite, 4),
                'convergence_vers_limite': convergence_vers_limite,
                'pourcentage_convergence': round((entropie_finale / entropie_theorique_limite) * 100, 1),
                'tendance_finale': round(tendance_finale, 6)
            },
            'performance': {
                'temps_calcul_secondes': round(temps_calcul, 3),
                'mains_par_seconde': round(len(mains) / temps_calcul, 1) if temps_calcul > 0 else 0
            }
        }
        
        # Mettre à jour les statistiques
        self.stats_calcul['parties_traitees'] += 1
        self.stats_calcul['temps_total'] += temps_calcul
        self.stats_calcul['temps_moyen_par_partie'] = self.stats_calcul['temps_total'] / self.stats_calcul['parties_traitees']
        
        print(f"✅ Partie {partie_id} traitée en {temps_calcul:.3f}s - Entropie finale: {entropie_finale:.4f}")
        
        return resultat
    
    def calculer_entropies_globales_dataset(self, dataset_path: str, nb_parties_max: int = None) -> Dict:
        """
        Calcule les entropies globales pour toutes les parties d'un dataset
        
        Args:
            dataset_path: Chemin vers le fichier JSON du dataset
            nb_parties_max: Nombre maximum de parties à traiter (None = toutes)
            
        Returns:
            Dict: Résultats pour toutes les parties
        """
        
        print(f"\n🔥 CALCUL ENTROPIES GLOBALES DATASET")
        print("=" * 50)
        print(f"📁 Dataset: {dataset_path}")
        
        # Charger le dataset
        try:
            with open(dataset_path, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
        except Exception as e:
            return {'erreur': f'Impossible de charger le dataset: {e}'}
        
        parties = dataset.get('parties', [])
        if nb_parties_max:
            parties = parties[:nb_parties_max]
        
        print(f"📊 {len(parties)} parties à traiter")
        
        # Traiter chaque partie
        resultats_parties = {}
        
        for i, partie in enumerate(parties, 1):
            print(f"\n🔄 Traitement partie {i}/{len(parties)}")
            
            resultat_partie = self.calculer_entropies_globales_partie(partie)
            
            if 'erreur' not in resultat_partie:
                partie_id = resultat_partie['partie_id']
                resultats_parties[partie_id] = resultat_partie
            else:
                print(f"❌ Erreur partie {i}: {resultat_partie['erreur']}")
        
        # Statistiques globales
        if resultats_parties:
            entropies_finales = [r['analyse_convergence']['entropie_finale'] for r in resultats_parties.values()]
            convergences = [r['analyse_convergence']['pourcentage_convergence'] for r in resultats_parties.values()]
            
            statistiques_globales = {
                'nb_parties_traitees': len(resultats_parties),
                'entropie_finale_moyenne': round(np.mean(entropies_finales), 4),
                'entropie_finale_mediane': round(np.median(entropies_finales), 4),
                'entropie_finale_ecart_type': round(np.std(entropies_finales), 4),
                'convergence_moyenne': round(np.mean(convergences), 1),
                'parties_convergentes': sum(1 for c in convergences if c > 85),
                'temps_total': round(self.stats_calcul['temps_total'], 2),
                'temps_moyen_par_partie': round(self.stats_calcul['temps_moyen_par_partie'], 3)
            }
        else:
            statistiques_globales = {'erreur': 'Aucune partie traitée avec succès'}
        
        resultat_final = {
            'dataset_path': dataset_path,
            'resultats_parties': resultats_parties,
            'statistiques_globales': statistiques_globales,
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")
        }
        
        print(f"\n✅ TRAITEMENT TERMINÉ")
        print(f"📊 {len(resultats_parties)} parties traitées avec succès")
        print(f"⏱️  Temps total: {self.stats_calcul['temps_total']:.2f}s")
        
        return resultat_final

    def analyser_evolution_entropique(self, resultat_partie: Dict) -> Dict:
        """
        Analyse l'évolution de l'entropie globale au cours d'une partie

        Args:
            resultat_partie: Résultat du calcul d'entropies globales

        Returns:
            Dict: Analyse de l'évolution entropique
        """

        entropies_globales = resultat_partie.get('entropies_globales', {})

        if not entropies_globales:
            return {'erreur': 'Aucune donnée d\'entropie disponible'}

        # Extraire les valeurs d'entropie
        positions = sorted(entropies_globales.keys())
        entropies = [entropies_globales[pos]['entropie_globale'] for pos in positions]

        # Analyser les phases d'évolution
        phases = []
        taille_fenetre = 10

        for i in range(taille_fenetre, len(entropies), taille_fenetre):
            debut = i - taille_fenetre
            fin = i

            entropies_phase = entropies[debut:fin]
            tendance = np.polyfit(range(len(entropies_phase)), entropies_phase, 1)[0]

            if tendance > 0.01:
                type_phase = "CROISSANCE"
            elif tendance < -0.01:
                type_phase = "DÉCROISSANCE"
            else:
                type_phase = "STABILITÉ"

            phases.append({
                'debut': debut + 1,
                'fin': fin,
                'type': type_phase,
                'tendance': round(tendance, 6),
                'entropie_debut': entropies[debut],
                'entropie_fin': entropies[fin-1]
            })

        # Points remarquables
        entropie_max = max(entropies)
        entropie_min = min(entropies)
        position_max = positions[entropies.index(entropie_max)]
        position_min = positions[entropies.index(entropie_min)]

        # Vitesse de convergence
        if len(entropies) >= 20:
            # Comparer les 10 premières et 10 dernières valeurs
            entropies_debut = entropies[:10]
            entropies_fin = entropies[-10:]

            vitesse_convergence = (np.mean(entropies_fin) - np.mean(entropies_debut)) / len(entropies)
        else:
            vitesse_convergence = 0.0

        return {
            'evolution_generale': {
                'entropie_initiale': entropies[0],
                'entropie_finale': entropies[-1],
                'variation_totale': entropies[-1] - entropies[0],
                'entropie_max': entropie_max,
                'entropie_min': entropie_min,
                'position_max': position_max,
                'position_min': position_min,
                'vitesse_convergence': round(vitesse_convergence, 6)
            },
            'phases_evolution': phases,
            'stabilite': {
                'variance': round(np.var(entropies), 4),
                'ecart_type': round(np.std(entropies), 4),
                'coefficient_variation': round(np.std(entropies) / np.mean(entropies), 4) if np.mean(entropies) > 0 else 0
            }
        }

    def calculer_ratio_desordre_temps_reel(self, resultat_partie: Dict, signatures_locales: Dict) -> Dict:
        """
        Calcule les ratios de désordre en temps réel pour une partie

        Args:
            resultat_partie: Résultats des entropies globales
            signatures_locales: Signatures entropiques des séquences locales

        Returns:
            Dict: Ratios de désordre et prédictions pour chaque main
        """

        entropies_globales = resultat_partie.get('entropies_globales', {})

        if not entropies_globales:
            return {'erreur': 'Aucune donnée d\'entropie globale disponible'}

        ratios_temps_reel = {}

        # Calculer les ratios pour chaque position où on a une signature locale
        for position, signature_locale in signatures_locales.items():
            if position in entropies_globales:

                entropie_locale = signature_locale.get('entropie_shannon', 0)
                entropie_globale = entropies_globales[position]['entropie_globale']

                # Calculer le ratio de désordre
                if entropie_globale > 0:
                    ratio_desordre = entropie_locale / entropie_globale
                else:
                    ratio_desordre = float('inf')

                # Prédire la tendance
                prediction_tendance = self._predire_tendance_desordre(ratio_desordre)

                ratios_temps_reel[position] = {
                    'entropie_locale': entropie_locale,
                    'entropie_globale': entropie_globale,
                    'ratio_desordre': round(ratio_desordre, 4),
                    'prediction_tendance': prediction_tendance,
                    'longueur_globale': entropies_globales[position]['longueur_sequence'],
                    'convergence_globale': entropies_globales[position]['pourcentage_entropie_max']
                }

        return {
            'partie_id': resultat_partie.get('partie_id'),
            'ratios_temps_reel': ratios_temps_reel,
            'nb_ratios_calcules': len(ratios_temps_reel)
        }

    def _predire_tendance_desordre(self, ratio_desordre: float, seuils: Tuple[float, float] = (0.7, 1.3)) -> Dict:
        """
        Prédit la tendance du désordre basée sur le ratio

        Args:
            ratio_desordre: Ratio entropie locale / entropie globale
            seuils: Seuils bas et haut pour la classification

        Returns:
            Dict: Prédiction de tendance avec confiance
        """

        seuil_bas, seuil_haut = seuils

        if ratio_desordre < seuil_bas:
            return {
                'tendance': 'VERS_PLUS_DE_DÉSORDRE',
                'intensité': 'FORTE' if ratio_desordre < 0.5 else 'MODÉRÉE',
                'confiance': min(95, (seuil_bas - ratio_desordre) * 100),
                'explication': 'Ordre local excessif → Correction vers chaos'
            }

        elif ratio_desordre > seuil_haut:
            return {
                'tendance': 'VERS_MOINS_DE_DÉSORDRE',
                'intensité': 'FORTE' if ratio_desordre > 1.5 else 'MODÉRÉE',
                'confiance': min(95, (ratio_desordre - seuil_haut) * 100),
                'explication': 'Chaos local excessif → Correction vers ordre'
            }

        else:
            return {
                'tendance': 'MAINTIEN_ÉQUILIBRE',
                'intensité': 'STABLE',
                'confiance': 70,
                'explication': 'Cohérence locale/globale → Continuation'
            }

    def exporter_resultats(self, resultats: Dict, format_export: str = 'json') -> str:
        """
        Exporte les résultats d'analyse entropique

        Args:
            resultats: Résultats à exporter
            format_export: 'json', 'txt', ou 'csv'

        Returns:
            str: Chemin du fichier exporté
        """

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if format_export == 'json':
            return self._exporter_json(resultats, timestamp)
        elif format_export == 'txt':
            return self._exporter_txt(resultats, timestamp)
        else:
            raise ValueError(f"Format d'export non supporté : {format_export}")

    def _exporter_json(self, resultats: Dict, timestamp: str) -> str:
        """Export au format JSON"""

        filename = f"entropies_globales_progressives_{timestamp}.json"
        filepath = self.cache_dir / filename

        # Convertir les types numpy et autres en types Python natifs
        resultats_serialisables = self._convertir_pour_json(resultats)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(resultats_serialisables, f, indent=2, ensure_ascii=False)

        print(f"✅ Résultats exportés : {filepath}")
        return str(filepath)

    def _convertir_pour_json(self, obj):
        """Convertit les objets en types sérialisables JSON"""

        if isinstance(obj, dict):
            return {str(k): self._convertir_pour_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convertir_pour_json(item) for item in obj]
        elif isinstance(obj, tuple):
            return list(self._convertir_pour_json(item) for item in obj)
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def _exporter_txt(self, resultats: Dict, timestamp: str) -> str:
        """Export au format TXT lisible"""

        filename = f"rapport_entropies_globales_{timestamp}.txt"
        filepath = self.cache_dir / filename

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("RAPPORT D'ANALYSE ENTROPIQUE GLOBALE PROGRESSIVE\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"Date de génération : {timestamp}\n\n")

            # Statistiques globales
            if 'statistiques_globales' in resultats:
                stats = resultats['statistiques_globales']
                f.write("STATISTIQUES GLOBALES\n")
                f.write("-" * 30 + "\n")
                f.write(f"Parties traitées : {stats.get('nb_parties_traitees', 0)}\n")
                f.write(f"Entropie finale moyenne : {stats.get('entropie_finale_moyenne', 0):.4f}\n")
                f.write(f"Convergence moyenne : {stats.get('convergence_moyenne', 0):.1f}%\n")
                f.write(f"Temps total : {stats.get('temps_total', 0):.2f}s\n\n")

            # Détail des parties (premières seulement pour éviter fichier trop volumineux)
            if 'resultats_parties' in resultats:
                parties = list(resultats['resultats_parties'].items())[:10]  # Premières 10 parties

                f.write("DÉTAIL DES PARTIES (10 premières)\n")
                f.write("-" * 40 + "\n")

                for partie_id, data in parties:
                    f.write(f"\nPartie {partie_id}:\n")
                    f.write(f"  Nombre de mains : {data.get('nb_mains_total', 0)}\n")
                    f.write(f"  Entropie finale : {data['analyse_convergence']['entropie_finale']:.4f}\n")
                    f.write(f"  Convergence : {data['analyse_convergence']['pourcentage_convergence']:.1f}%\n")
                    f.write(f"  Temps calcul : {data['performance']['temps_calcul_secondes']:.3f}s\n")

        print(f"✅ Rapport exporté : {filepath}")
        return str(filepath)


def main():
    """Fonction principale pour tester le module"""

    print("🎯 TEST DU MODULE ENTROPIE GLOBALE PROGRESSIVE")
    print("=" * 55)

    # Créer le calculateur
    calculateur = CalculateurEntropieGlobaleProgressive()

    # Test avec une partie fictive
    print("\n1. Test avec une partie fictive...")

    partie_test = {
        'partie_number': 'TEST_001',
        'mains': [
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_B_PLAYER'},
            {'index5_combined': '1_C_TIE'},
            {'index5_combined': '0_A_BANKER'},
            {'index5_combined': '0_B_PLAYER'},
            {'index5_combined': '0_C_TIE'},
            {'index5_combined': '1_A_BANKER'},
            {'index5_combined': '1_B_PLAYER'},
            {'index5_combined': '1_C_TIE'}
        ]
    }

    # Calculer les entropies globales
    resultat_test = calculateur.calculer_entropies_globales_partie(partie_test)

    if 'erreur' not in resultat_test:
        print(f"✅ Test réussi - {resultat_test['nb_mains_total']} mains traitées")
        print(f"   Entropie finale : {resultat_test['analyse_convergence']['entropie_finale']:.4f}")
        print(f"   Convergence : {resultat_test['analyse_convergence']['pourcentage_convergence']:.1f}%")

        # Afficher quelques valeurs d'entropie
        print("\n   Évolution entropique :")
        entropies = resultat_test['entropies_globales']
        for pos in [1, 3, 5, 7, 10]:
            if pos in entropies:
                print(f"     Main {pos}: {entropies[pos]['entropie_globale']:.4f}")

        # Test d'analyse d'évolution
        print("\n2. Test d'analyse d'évolution...")
        evolution = calculateur.analyser_evolution_entropique(resultat_test)

        if 'erreur' not in evolution:
            print(f"✅ Analyse d'évolution réussie")
            print(f"   Variation totale : {evolution['evolution_generale']['variation_totale']:.4f}")
            print(f"   Écart-type : {evolution['stabilite']['ecart_type']:.4f}")

        # Export des résultats
        print("\n3. Test d'export...")
        fichier_json = calculateur.exporter_resultats(resultat_test, 'json')
        fichier_txt = calculateur.exporter_resultats(resultat_test, 'txt')

        print(f"✅ Fichiers exportés :")
        print(f"   JSON : {fichier_json}")
        print(f"   TXT : {fichier_txt}")

    else:
        print(f"❌ Erreur dans le test : {resultat_test['erreur']}")

    print(f"\n🎯 TEST TERMINÉ")
    print(f"📊 Statistiques : {calculateur.stats_calcul}")


def test_avec_dataset_reel(dataset_path: str, nb_parties: int = 5):
    """Test avec un dataset réel (optionnel)"""

    print(f"\n🔥 TEST AVEC DATASET RÉEL")
    print("=" * 40)
    print(f"📁 Dataset : {dataset_path}")
    print(f"📊 Parties à traiter : {nb_parties}")

    calculateur = CalculateurEntropieGlobaleProgressive()

    # Traiter le dataset
    resultats = calculateur.calculer_entropies_globales_dataset(dataset_path, nb_parties)

    if 'erreur' not in resultats:
        print(f"\n✅ Dataset traité avec succès")

        stats = resultats['statistiques_globales']
        print(f"📊 Parties traitées : {stats['nb_parties_traitees']}")
        print(f"📈 Entropie finale moyenne : {stats['entropie_finale_moyenne']:.4f}")
        print(f"🎯 Convergence moyenne : {stats['convergence_moyenne']:.1f}%")
        print(f"⏱️  Temps total : {stats['temps_total']:.2f}s")

        # Export
        fichier_export = calculateur.exporter_resultats(resultats, 'json')
        print(f"💾 Résultats exportés : {fichier_export}")

        return resultats
    else:
        print(f"❌ Erreur : {resultats['erreur']}")
        return None


if __name__ == "__main__":
    # Test principal
    main()

    # Test optionnel avec dataset réel (décommenter si nécessaire)
    # dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    # if os.path.exists(dataset_path):
    #     test_avec_dataset_reel(dataset_path, nb_parties=3)
    # else:
    #     print(f"\n⚠️  Dataset {dataset_path} non trouvé - Test avec dataset réel ignoré")

    print(f"\n🎉 TOUS LES TESTS TERMINÉS !")
