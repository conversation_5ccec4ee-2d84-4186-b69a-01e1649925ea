#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Analyse Complète Corrigée - Sans Émojis Unicode
CORRECTION DES ERREURS D'ENCODAGE

Ce script lance toutes les analyses sans émojis Unicode
pour éviter les erreurs d'encodage Windows.

Auteur: Expert Statisticien
Date: 2025-06-23
"""

import sys
import os
import subprocess
from datetime import datetime

class AnalyseurCompletCorrige:
    """
    Analyseur complet corrigé sans émojis Unicode
    """
    
    def __init__(self):
        """Initialise l'analyseur corrigé"""
        self.dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
        self.resultats = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        print("ANALYSEUR COMPLET CORRIGE INITIALISE")
        print("=" * 50)
        print("Objectif: Lancer toutes les analyses sans erreurs Unicode")
        print("Dataset: 100,000 parties")
        print("=" * 50)
    
    def lancer_analyse_complete_corrigee(self):
        """Lance toutes les analyses corrigées"""
        print("\nLANCEMENT ANALYSE COMPLETE CORRIGEE")
        print("=" * 40)
        
        try:
            # PHASE 1: Analyse évolution ratios
            print("\nPHASE 1: ANALYSE EVOLUTION RATIOS")
            print("-" * 30)
            self.resultats['evolution_ratios'] = self._analyser_evolution_ratios_corrige()
            
            # PHASE 2: Analyse décroissance manuelle
            print("\nPHASE 2: ANALYSE DECROISSANCE MANUELLE")
            print("-" * 35)
            self.resultats['decroissance'] = self._analyser_decroissance_manuelle()
            
            # PHASE 3: Validation logique solutionpotentielle
            print("\nPHASE 3: VALIDATION LOGIQUE SOLUTIONPOTENTIELLE")
            print("-" * 45)
            self.resultats['validation'] = self._valider_logique_solutionpotentielle()
            
            # PHASE 4: Génération rapport final
            print("\nPHASE 4: GENERATION RAPPORT FINAL")
            print("-" * 30)
            rapport_final = self._generer_rapport_final_corrige()
            
            print(f"\nANALYSE COMPLETE CORRIGEE TERMINEE !")
            print(f"Rapport final: {rapport_final}")
            
            return True
            
        except Exception as e:
            print(f"Erreur analyse corrigée: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _analyser_evolution_ratios_corrige(self):
        """Analyse l'évolution des ratios sans erreurs Unicode"""
        print("Lancement analyse évolution ratios...")
        
        try:
            from analyseur_transitions_index5 import AnalyseurEvolutionEntropique, AnalyseurEvolutionRatios
            
            # Initialiser les analyseurs
            print("Initialisation analyseur entropique...")
            analyseur_entropique = AnalyseurEvolutionEntropique(self.dataset_path)
            
            # Vérifier si l'analyse entropique est déjà faite
            if not hasattr(analyseur_entropique, 'evolutions_entropiques') or not analyseur_entropique.evolutions_entropiques:
                print("Analyse entropique en cours...")
                resultats_entropiques = analyseur_entropique.analyser_toutes_parties_entropiques(nb_parties_max=100000)
                print(f"Parties analysées: {resultats_entropiques['parties_reussies']:,}")
            else:
                print("Données entropiques déjà disponibles")
            
            # Analyser les ratios
            print("Initialisation analyseur ratios...")
            analyseur_ratios = AnalyseurEvolutionRatios(analyseur_entropique)
            
            if not hasattr(analyseur_ratios, 'evolutions_ratios') or not analyseur_ratios.evolutions_ratios:
                print("Analyse ratios en cours...")
                analyseur_ratios.analyser_evolution_toutes_parties()
                print(f"Ratios calculés pour {len(analyseur_ratios.evolutions_ratios):,} parties")
            else:
                print("Données ratios déjà disponibles")
            
            # Générer rapport évolution (méthode corrigée)
            print("Génération rapport évolution...")
            rapport_evolution = analyseur_ratios.generer_rapport_evolution_complete()
            
            return {
                'status': 'SUCCESS',
                'parties_analysees': len(analyseur_ratios.evolutions_ratios),
                'rapport_evolution': rapport_evolution,
                'analyseur_ratios': analyseur_ratios
            }
            
        except Exception as e:
            print(f"Erreur analyse évolution ratios: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _analyser_decroissance_manuelle(self):
        """Analyse manuelle de la décroissance des ratios"""
        print("Analyse décroissance manuelle...")
        
        try:
            if 'evolution_ratios' not in self.resultats or self.resultats['evolution_ratios']['status'] != 'SUCCESS':
                return {'status': 'ERROR', 'error': 'Analyse ratios non disponible'}
            
            analyseur_ratios = self.resultats['evolution_ratios']['analyseur_ratios']
            
            # Analyser les tendances de décroissance
            print("Calcul tendances décroissance...")
            tendances = {
                'parties_avec_decroissance_l4': 0,
                'parties_avec_decroissance_l5': 0,
                'parties_avec_decroissance_both': 0,
                'moyenne_decroissance_l4': 0.0,
                'moyenne_decroissance_l5': 0.0,
                'total_parties': 0
            }
            
            for partie_id, evolution in analyseur_ratios.evolutions_ratios.items():
                if 'erreur' in evolution:
                    continue
                
                ratios_l4 = evolution.get('ratios_l4', [])
                ratios_l5 = evolution.get('ratios_l5', [])
                
                if len(ratios_l4) >= 2 and len(ratios_l5) >= 2:
                    tendances['total_parties'] += 1
                    
                    # Analyser décroissance L4
                    decroissance_l4 = ratios_l4[0] - ratios_l4[-1]  # Premier - Dernier
                    if decroissance_l4 > 0:
                        tendances['parties_avec_decroissance_l4'] += 1
                    tendances['moyenne_decroissance_l4'] += decroissance_l4
                    
                    # Analyser décroissance L5
                    decroissance_l5 = ratios_l5[0] - ratios_l5[-1]  # Premier - Dernier
                    if decroissance_l5 > 0:
                        tendances['parties_avec_decroissance_l5'] += 1
                    tendances['moyenne_decroissance_l5'] += decroissance_l5
                    
                    # Décroissance des deux
                    if decroissance_l4 > 0 and decroissance_l5 > 0:
                        tendances['parties_avec_decroissance_both'] += 1
            
            # Calculer moyennes et pourcentages
            if tendances['total_parties'] > 0:
                tendances['moyenne_decroissance_l4'] /= tendances['total_parties']
                tendances['moyenne_decroissance_l5'] /= tendances['total_parties']
                
                tendances['pct_decroissance_l4'] = (tendances['parties_avec_decroissance_l4'] / tendances['total_parties']) * 100
                tendances['pct_decroissance_l5'] = (tendances['parties_avec_decroissance_l5'] / tendances['total_parties']) * 100
                tendances['pct_decroissance_both'] = (tendances['parties_avec_decroissance_both'] / tendances['total_parties']) * 100
            
            print(f"Décroissance L4: {tendances['pct_decroissance_l4']:.1f}% des parties")
            print(f"Décroissance L5: {tendances['pct_decroissance_l5']:.1f}% des parties")
            print(f"Décroissance Both: {tendances['pct_decroissance_both']:.1f}% des parties")
            
            return {
                'status': 'SUCCESS',
                'tendances': tendances
            }
            
        except Exception as e:
            print(f"Erreur analyse décroissance: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _valider_logique_solutionpotentielle(self):
        """Valide la logique selon solutionpotentielle.txt"""
        print("Validation logique solutionpotentielle...")
        
        try:
            if 'decroissance' not in self.resultats or self.resultats['decroissance']['status'] != 'SUCCESS':
                return {'status': 'ERROR', 'error': 'Analyse décroissance non disponible'}
            
            tendances = self.resultats['decroissance']['tendances']
            
            validation = {
                'logique_decroissance_validee': False,
                'seuil_decroissance_atteint': False,
                'coherence_l4_l5': False,
                'recommandation_predicteur': ''
            }
            
            # Critères de validation selon solutionpotentielle.txt
            seuil_decroissance_min = 60.0  # 60% des parties doivent montrer décroissance
            
            # Validation logique décroissance
            if tendances['pct_decroissance_both'] >= seuil_decroissance_min:
                validation['logique_decroissance_validee'] = True
                validation['seuil_decroissance_atteint'] = True
                print(f"Logique décroissance validée: {tendances['pct_decroissance_both']:.1f}% >= {seuil_decroissance_min}%")
            else:
                print(f"Logique décroissance non validée: {tendances['pct_decroissance_both']:.1f}% < {seuil_decroissance_min}%")
            
            # Cohérence L4/L5
            diff_decroissance = abs(tendances['pct_decroissance_l4'] - tendances['pct_decroissance_l5'])
            if diff_decroissance < 10.0:  # Différence < 10%
                validation['coherence_l4_l5'] = True
                print(f"Cohérence L4/L5 validée: différence {diff_decroissance:.1f}% < 10%")
            else:
                print(f"Cohérence L4/L5 non validée: différence {diff_decroissance:.1f}% >= 10%")
            
            # Recommandation pour le prédicteur
            if validation['logique_decroissance_validee'] and validation['coherence_l4_l5']:
                validation['recommandation_predicteur'] = "IMPLEMENTER_LOGIQUE_DECROISSANCE"
                print("Recommandation: Implémenter logique décroissance dans prédicteur")
            elif validation['logique_decroissance_validee']:
                validation['recommandation_predicteur'] = "IMPLEMENTER_AVEC_PRUDENCE"
                print("Recommandation: Implémenter avec prudence (incohérence L4/L5)")
            else:
                validation['recommandation_predicteur'] = "NE_PAS_IMPLEMENTER"
                print("Recommandation: Ne pas implémenter logique décroissance")
            
            return {
                'status': 'SUCCESS',
                'validation': validation
            }
            
        except Exception as e:
            print(f"Erreur validation: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _generer_rapport_final_corrige(self):
        """Génère le rapport final corrigé"""
        nom_rapport = f"rapport_analyse_corrigee_{self.timestamp}.txt"
        
        with open(nom_rapport, 'w', encoding='utf-8') as f:
            f.write("RAPPORT ANALYSE COMPLETE CORRIGEE\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Dataset: {self.dataset_path}\n")
            f.write(f"Parties analysées: 100,000\n\n")
            
            # RÉSULTATS ÉVOLUTION RATIOS
            if 'evolution_ratios' in self.resultats:
                result = self.resultats['evolution_ratios']
                f.write("EVOLUTION RATIOS\n")
                f.write("-" * 20 + "\n")
                f.write(f"Statut: {result['status']}\n")
                if result['status'] == 'SUCCESS':
                    f.write(f"Parties analysées: {result['parties_analysees']:,}\n")
                    f.write(f"Rapport généré: {result['rapport_evolution']}\n")
                else:
                    f.write(f"Erreur: {result['error']}\n")
                f.write("\n")
            
            # RÉSULTATS DÉCROISSANCE
            if 'decroissance' in self.resultats:
                result = self.resultats['decroissance']
                f.write("ANALYSE DECROISSANCE\n")
                f.write("-" * 25 + "\n")
                f.write(f"Statut: {result['status']}\n")
                if result['status'] == 'SUCCESS':
                    tendances = result['tendances']
                    f.write(f"Total parties: {tendances['total_parties']:,}\n")
                    f.write(f"Décroissance L4: {tendances['pct_decroissance_l4']:.1f}%\n")
                    f.write(f"Décroissance L5: {tendances['pct_decroissance_l5']:.1f}%\n")
                    f.write(f"Décroissance Both: {tendances['pct_decroissance_both']:.1f}%\n")
                    f.write(f"Moyenne décroissance L4: {tendances['moyenne_decroissance_l4']:.6f}\n")
                    f.write(f"Moyenne décroissance L5: {tendances['moyenne_decroissance_l5']:.6f}\n")
                else:
                    f.write(f"Erreur: {result['error']}\n")
                f.write("\n")
            
            # RÉSULTATS VALIDATION
            if 'validation' in self.resultats:
                result = self.resultats['validation']
                f.write("VALIDATION LOGIQUE SOLUTIONPOTENTIELLE\n")
                f.write("-" * 40 + "\n")
                f.write(f"Statut: {result['status']}\n")
                if result['status'] == 'SUCCESS':
                    validation = result['validation']
                    f.write(f"Logique décroissance validée: {validation['logique_decroissance_validee']}\n")
                    f.write(f"Seuil décroissance atteint: {validation['seuil_decroissance_atteint']}\n")
                    f.write(f"Cohérence L4/L5: {validation['coherence_l4_l5']}\n")
                    f.write(f"Recommandation prédicteur: {validation['recommandation_predicteur']}\n")
                else:
                    f.write(f"Erreur: {result['error']}\n")
                f.write("\n")
            
            # CONCLUSIONS FINALES
            f.write("CONCLUSIONS FINALES\n")
            f.write("-" * 20 + "\n")
            
            analyses_reussies = sum(1 for r in self.resultats.values() if r.get('status') == 'SUCCESS')
            f.write(f"Analyses réussies: {analyses_reussies}/{len(self.resultats)}\n")
            
            if 'validation' in self.resultats and self.resultats['validation']['status'] == 'SUCCESS':
                validation = self.resultats['validation']['validation']
                if validation['logique_decroissance_validee']:
                    f.write("CONCLUSION: Logique décroissance VALIDÉE\n")
                    f.write("ACTION: Implémenter prédicteur basé sur décroissance\n")
                else:
                    f.write("CONCLUSION: Logique décroissance NON VALIDÉE\n")
                    f.write("ACTION: Utiliser tableau prédictif S/O existant\n")
            else:
                f.write("CONCLUSION: Validation impossible\n")
                f.write("ACTION: Utiliser tableau prédictif S/O existant\n")
        
        print(f"Rapport final généré: {nom_rapport}")
        return nom_rapport

def main():
    """Fonction principale"""
    print("LANCEMENT ANALYSE COMPLETE CORRIGEE")
    print("=" * 40)
    print("Objectif: Analyser sans erreurs Unicode")
    print("Dataset: 100,000 parties")
    print("=" * 40)
    
    # Créer et lancer l'analyseur corrigé
    analyseur = AnalyseurCompletCorrige()
    success = analyseur.lancer_analyse_complete_corrigee()
    
    if success:
        print(f"\nANALYSE COMPLETE CORRIGEE REUSSIE !")
        print("Toutes les analyses lancées sans erreurs Unicode")
        print("Rapport final généré")
        print("Validation logique solutionpotentielle terminée")
    else:
        print(f"\nANALYSE COMPLETE CORRIGEE ECHOUEE")
        print("Vérifiez les erreurs dans les logs")
    
    print("\n" + "=" * 40)

if __name__ == "__main__":
    main()
