#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test msgspec exclusif - Vérification qu'il n'y a aucun conflit
"""

import os
import time

def test_msgspec_exclusif():
    """Test que seul msgspec est utilisé"""
    print("🧪 TEST MSGSPEC EXCLUSIF")
    print("=" * 50)
    
    # Vérifier msgspec
    try:
        import msgspec.json
        print("✅ msgspec disponible")
    except ImportError:
        print("❌ msgspec non disponible")
        return False
    
    # Vérifier le fichier
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé : {dataset_path}")
        return False
    
    print(f"✅ Dataset trouvé : {dataset_path}")
    
    # Test du chargeur centralisé
    try:
        from analyse_complete_avec_diff import ChargeurJSONCentralise
        print("✅ ChargeurJSONCentralise importé")
        
        # Test chargement
        print("🔄 Test chargement msgspec...")
        debut = time.time()
        
        dataset = ChargeurJSONCentralise.charger_dataset_optimal(dataset_path)
        
        fin = time.time()
        temps = fin - debut
        
        nb_parties = len(dataset.get('parties', []))
        print(f"✅ Chargement réussi : {nb_parties:,} parties en {temps:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False

if __name__ == "__main__":
    success = test_msgspec_exclusif()
    if success:
        print("\n🎉 TEST RÉUSSI : msgspec fonctionne parfaitement")
    else:
        print("\n❌ TEST ÉCHOUÉ : problème avec msgspec")
