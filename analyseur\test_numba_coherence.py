#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Cohérence Numba - Vérification des incohérences JIT
"""

def test_coherence_numba():
    """Test des incohérences Numba dans les modules"""
    print("🧪 TEST COHÉRENCE NUMBA")
    print("=" * 50)
    
    # Test 1 : Module centralisé
    print("\n📊 TEST 1 : Module Numba centralisé")
    print("-" * 30)
    try:
        from numba_centralise import HAS_NUMBA, afficher_statut_numba
        print("✅ Module numba_centralise importé")
        statut_centralise = afficher_statut_numba()
    except ImportError as e:
        print(f"❌ Erreur import numba_centralise : {e}")
        statut_centralise = False
    
    # Test 2 : analyse_complete_avec_diff
    print("\n📊 TEST 2 : analyse_complete_avec_diff")
    print("-" * 30)
    try:
        # Compter les messages Numba
        with open("analyse_complete_avec_diff.py", 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        messages_numba = []
        if "🚀 Numba JIT disponible" in contenu:
            messages_numba.append("🚀 Numba JIT disponible")
        if "🚀 Fonctions JIT ultra-optimisées compilées" in contenu:
            messages_numba.append("🚀 Fonctions JIT ultra-optimisées compilées")
        if "HAS_NUMBA = True" in contenu:
            messages_numba.append("HAS_NUMBA = True")
        
        print(f"📊 Messages Numba trouvés : {len(messages_numba)}")
        for msg in messages_numba:
            print(f"   - {msg}")
            
    except Exception as e:
        print(f"❌ Erreur analyse fichier : {e}")
    
    # Test 3 : analyseur_transitions_index5
    print("\n📊 TEST 3 : analyseur_transitions_index5")
    print("-" * 30)
    try:
        # Compter les messages Numba
        with open("analyseur_transitions_index5.py", 'r', encoding='utf-8') as f:
            contenu = f.read()
        
        messages_numba = []
        if "🚀 Numba disponible - JIT compilation ULTRA-RAPIDE activée" in contenu:
            messages_numba.append("🚀 Numba disponible - JIT compilation ULTRA-RAPIDE activée")
        if "🚀 Fonctions Numba JIT compilées - Performance ULTRA-RAPIDE activée" in contenu:
            messages_numba.append("🚀 Fonctions Numba JIT compilées - Performance ULTRA-RAPIDE activée")
        if "HAS_NUMBA = True" in contenu:
            messages_numba.append("HAS_NUMBA = True")
        
        print(f"📊 Messages Numba trouvés : {len(messages_numba)}")
        for msg in messages_numba:
            print(f"   - {msg}")
            
    except Exception as e:
        print(f"❌ Erreur analyse fichier : {e}")
    
    # Test 4 : Import des modules principaux
    print("\n📊 TEST 4 : Import modules principaux")
    print("-" * 30)
    try:
        # Capturer les messages d'import
        import io
        import sys
        from contextlib import redirect_stdout, redirect_stderr
        
        output = io.StringIO()
        with redirect_stdout(output), redirect_stderr(output):
            # Import qui va afficher les messages Numba
            import analyse_complete_avec_diff
        
        messages = output.getvalue()
        print("📊 Messages lors de l'import :")
        for ligne in messages.split('\n'):
            if ligne.strip() and ('Numba' in ligne or 'JIT' in ligne):
                print(f"   {ligne.strip()}")
                
    except Exception as e:
        print(f"❌ Erreur import : {e}")
    
    # Résumé
    print("\n" + "=" * 50)
    print("📋 RÉSUMÉ DES INCOHÉRENCES DÉTECTÉES")
    print("=" * 50)
    print("🚨 PROBLÈMES IDENTIFIÉS :")
    print("   1. Double déclaration de Numba dans 2 fichiers")
    print("   2. Messages d'activation multiples et redondants")
    print("   3. Variables HAS_NUMBA séparées")
    print("   4. Fonctions JIT dispersées dans plusieurs modules")
    print("")
    print("🔧 SOLUTION RECOMMANDÉE :")
    print("   1. Utiliser uniquement numba_centralise.py")
    print("   2. Supprimer les déclarations Numba des autres fichiers")
    print("   3. Importer depuis le module centralisé")
    print("   4. Un seul message d'activation au démarrage")

if __name__ == "__main__":
    test_coherence_numba()
