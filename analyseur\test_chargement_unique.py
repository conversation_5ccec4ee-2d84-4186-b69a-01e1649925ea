#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du Chargement Unique - Vérification de l'optimisation
==========================================================

Ce script teste que le fichier JSON n'est chargé qu'une seule fois
avec la technique optimale msgspec.

Auteur: Expert Statisticien
Date: 2025-06-24
"""

import sys
import os
import time
from datetime import datetime

def test_chargement_unique():
    """
    Test principal : vérifier qu'il n'y a qu'un seul chargement JSON
    """
    print("🧪 TEST CHARGEMENT UNIQUE - Vérification optimisation")
    print("=" * 60)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Vérifier que msgspec est disponible
    try:
        import msgspec.json
        print("✅ msgspec disponible - Technique optimale activée")
    except ImportError:
        print("❌ msgspec non disponible - Test impossible")
        print("🔧 Installation requise : pip install msgspec")
        return False
    
    # Vérifier que le dataset existe
    dataset_path = "dataset_baccarat_lupasco_20250622_011427.json"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset non trouvé : {dataset_path}")
        return False
    
    print(f"✅ Dataset trouvé : {dataset_path}")
    
    # Import du module principal
    try:
        from analyse_complete_avec_diff import (
            ChargeurJSONCentralise, 
            CACHE_RAM_GLOBAL, 
            MONITEUR_CHARGEMENTS,
            analyser_conditions_predictives_so_avec_diff
        )
        print("✅ Modules importés avec succès")
    except ImportError as e:
        print(f"❌ Erreur import : {e}")
        return False
    
    # PHASE 1 : Test du chargeur centralisé
    print("\n📊 PHASE 1 : Test du chargeur centralisé")
    print("-" * 40)
    
    debut_test = time.time()
    
    try:
        # Vérifier msgspec
        if not ChargeurJSONCentralise.verifier_msgspec():
            print("❌ msgspec non disponible")
            return False
        
        # Premier chargement
        print("🔄 Premier chargement avec ChargeurJSONCentralise...")
        dataset_json = ChargeurJSONCentralise.charger_dataset_optimal(dataset_path)
        
        nb_parties = len(dataset_json.get('parties', []))
        print(f"✅ Premier chargement réussi : {nb_parties:,} parties")
        
    except Exception as e:
        print(f"❌ Erreur lors du chargement : {e}")
        return False
    
    # PHASE 2 : Test du cache RAM global
    print("\n📊 PHASE 2 : Test du cache RAM global")
    print("-" * 40)
    
    try:
        # Vider le cache pour test propre
        CACHE_RAM_GLOBAL.vider_cache()
        
        # Chargement via cache
        print("🔄 Chargement via CACHE_RAM_GLOBAL...")
        analyseur_entropique, analyseur_ratios, donnees_analyse, donnees_volatilite = CACHE_RAM_GLOBAL.charger_dataset_unique(dataset_path)
        
        print(f"✅ Cache chargé : {len(donnees_analyse)} analyses, {len(donnees_volatilite)} volatilités")
        
        # Test accès multiple au cache
        print("🔄 Test accès multiple au cache...")
        for i in range(3):
            _, _, donnees_test, _ = CACHE_RAM_GLOBAL.charger_dataset_unique(dataset_path)
            print(f"   Accès #{i+2} : {len(donnees_test)} données (depuis cache)")
        
        # Afficher statistiques du cache
        CACHE_RAM_GLOBAL.afficher_statistiques_cache()
        
    except Exception as e:
        print(f"❌ Erreur cache RAM : {e}")
        return False
    
    # PHASE 3 : Test de l'analyse complète
    print("\n📊 PHASE 3 : Test de l'analyse complète")
    print("-" * 40)
    
    try:
        # Lancer l'analyse complète
        print("🔄 Lancement de l'analyse complète...")
        resultat = analyser_conditions_predictives_so_avec_diff()
        
        if resultat:
            print("✅ Analyse complète terminée avec succès")
        else:
            print("⚠️ Analyse complète terminée avec avertissements")
        
    except Exception as e:
        print(f"❌ Erreur analyse complète : {e}")
        return False
    
    # PHASE 4 : Vérification du moniteur de chargements
    print("\n📊 PHASE 4 : Vérification du moniteur")
    print("-" * 40)
    
    # Afficher le rapport du moniteur
    MONITEUR_CHARGEMENTS.afficher_rapport()
    
    # Vérifier qu'il n'y a eu qu'un seul chargement
    if MONITEUR_CHARGEMENTS.nb_chargements_json == 1:
        print("🎉 SUCCÈS : Un seul chargement JSON détecté !")
        resultat_final = True
    else:
        print(f"❌ ÉCHEC : {MONITEUR_CHARGEMENTS.nb_chargements_json} chargements détectés !")
        print("🔍 Vérifiez les modules pour des rechargements cachés")
        resultat_final = False
    
    # Temps total du test
    fin_test = time.time()
    temps_total = fin_test - debut_test
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DU TEST")
    print("=" * 60)
    print(f"⏱️  Temps total : {temps_total:.2f}s")
    print(f"🔢 Chargements JSON : {MONITEUR_CHARGEMENTS.nb_chargements_json}")
    print(f"💾 Cache actif : {'✅' if CACHE_RAM_GLOBAL.cache_actif else '❌'}")
    print(f"📊 Données en cache : {len(CACHE_RAM_GLOBAL.donnees_analyse_cache) if CACHE_RAM_GLOBAL.cache_actif else 0}")
    
    if resultat_final:
        print("🎉 TEST RÉUSSI : Chargement unique optimisé !")
        print("✅ msgspec utilisé avec succès")
        print("✅ Aucun rechargement multiple détecté")
        print("✅ Cache RAM fonctionnel")
    else:
        print("❌ TEST ÉCHOUÉ : Optimisation incomplète")
        print("🔧 Vérifiez les modules pour des chargements cachés")
    
    print("=" * 60)
    return resultat_final

def test_tentative_rechargement():
    """
    Test que les méthodes de rechargement sont bien bloquées
    """
    print("\n🧪 TEST BLOCAGE RECHARGEMENTS")
    print("-" * 40)
    
    try:
        from analyseur_transitions_index5 import AnalyseurEvolutionEntropique
        
        # Tenter de créer un analyseur sans dataset_precharge
        print("🔄 Test création analyseur sans dataset_precharge...")
        try:
            analyseur = AnalyseurEvolutionEntropique("test.json", dataset_precharge=None)
            print("❌ ÉCHEC : Analyseur créé sans dataset_precharge !")
            return False
        except ValueError as e:
            print("✅ SUCCÈS : Création bloquée comme attendu")
            print(f"   Message : {str(e)[:100]}...")
        
        # Tester les méthodes de chargement supprimées
        print("🔄 Test méthodes de chargement supprimées...")
        try:
            from analyseur_transitions_index5 import AnalyseurEvolutionRatios
            
            # Créer une instance factice pour tester
            class TestAnalyseur:
                def __init__(self):
                    self.dataset_path = "test.json"
                
                def _charger_avec_orjson(self, nb_parties_max=None):
                    # Importer la méthode depuis AnalyseurEvolutionRatios
                    from analyseur_transitions_index5 import AnalyseurEvolutionRatios
                    instance = AnalyseurEvolutionRatios.__new__(AnalyseurEvolutionRatios)
                    return instance._charger_avec_orjson(nb_parties_max)
            
            test_analyseur = TestAnalyseur()
            test_analyseur._charger_avec_orjson()
            print("❌ ÉCHEC : Méthode de chargement non bloquée !")
            return False
            
        except RuntimeError as e:
            print("✅ SUCCÈS : Méthodes de chargement bloquées")
            print(f"   Message : {str(e)[:100]}...")
        except Exception as e:
            print(f"⚠️ Erreur inattendue : {e}")
        
        print("✅ Test blocage rechargements réussi")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test blocage : {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 SUITE DE TESTS - CHARGEMENT UNIQUE OPTIMISÉ")
    print("=" * 70)
    
    # Test 1 : Chargement unique
    test1_ok = test_chargement_unique()
    
    # Test 2 : Blocage rechargements
    test2_ok = test_tentative_rechargement()
    
    # Résultat final
    print("\n" + "=" * 70)
    print("🏁 RÉSULTAT FINAL")
    print("=" * 70)
    
    if test1_ok and test2_ok:
        print("🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Optimisation chargement unique : FONCTIONNELLE")
        print("✅ Blocage rechargements multiples : ACTIF")
        print("✅ Technique msgspec : UTILISÉE")
        print("\n🚀 Le système est prêt pour l'analyse optimisée !")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print(f"   Test chargement unique : {'✅' if test1_ok else '❌'}")
        print(f"   Test blocage rechargements : {'✅' if test2_ok else '❌'}")
        print("\n🔧 Vérifiez les erreurs ci-dessus")
    
    print("=" * 70)
    return test1_ok and test2_ok

if __name__ == "__main__":
    main()
