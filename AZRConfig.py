################################################################################
#                                                                              #
#  📋 SECTION : CONFIGURATION CENTRALISÉE AZR                                  #
#                                                                              #
################################################################################

class AZRConfig:
    """
    Configuration centralisée AZR pour le système de comptage Baccarat

    PRINCIPE : AUCUNE valeur codée en dur dans les méthodes
    Toutes les constantes, valeurs et paramètres sont centralisés ici
    """

    def __init__(self):
        # ====================================================================
        # SYSTÈME DE COMPTAGE BACCARAT (INDEX 1, 2, 3, 5)
        # ====================================================================

        # INDEX 2 : Comptage PAIR/IMPAIR des cartes
        self.card_count_categories = {
            'A': 4,    # Aucune 3ème carte (pair_4)
            'B': 6,    # Deux 3èmes cartes (pair_6)
            'C': 5     # Une 3ème carte (impair_5)
        }

        # Valeurs numériques pour calculs
        self.card_counts = {
            'min': 4,
            'max': 6,
            'pair_values': [4, 6],
            'impair_values': [5]
        }

        # Brûlage possible (2-11 cartes)
        self.burn_card_range = {
            'min': 2,
            'max': 11,
            'categories': {
                'pair_2': 2, 'pair_4': 4, 'pair_6': 6, 'pair_8': 8, 'pair_10': 10,
                'impair_3': 3, 'impair_5': 5, 'impair_7': 7, 'impair_9': 9, 'impair_11': 11
            }
        }

        # Valeurs par défaut pour brûlage
        self.burn_defaults = {
            'PAIR': 4,      # A par défaut (pair_4)
            'IMPAIR': 5     # C par défaut (impair_5)
        }

        # INDEX 1 : États SYNC/DESYNC
        self.sync_states = [0, 1]  # 0=SYNC, 1=DESYNC
        self.initial_sync_mapping = {
            'PAIR': 0,    # SYNC
            'IMPAIR': 1   # DESYNC
        }

        # INDEX 3 : Résultats P/B/T
        self.game_results = ['PLAYER', 'BANKER', 'TIE']
        self.pb_results = ['PLAYER', 'BANKER']  # Pour calculs P/B

        # Parités
        self.parities = ['PAIR', 'IMPAIR']

        # ====================================================================
        # LIMITES ET CONTRAINTES
        # ====================================================================

        # Limites par partie
        self.max_manches_per_game = 60  # Fenêtre de 60 manches (2^60 possibilités)
        self.max_hands_per_game = 100   # Incluant TIE

        # Valeurs spéciales
        self.burn_hand_number = 0       # Numéro de la main de brûlage
        self.burn_pb_hand_number = 0    # Numéro de manche pour brûlage
        self.first_game_number = 1      # Numéro de la première partie

        # ====================================================================
        # INTERFACE GRAPHIQUE
        # ====================================================================

        # Dimensions fenêtre principale
        self.window_geometry = "1000x700"

        # Couleurs interface
        self.colors = {
            'player': "#1E3A8A",    # Bleu
            'banker': "#B91C1C",    # Rouge
            'tie': "#166534",       # Vert
            'burn_bg': "black",     # Fond boutons brûlage
            'burn_fg': "yellow",    # Texte boutons brûlage
            'text_light': "#F0F0F0" # Texte clair
        }

        # Polices
        self.fonts = {
            'title': ("Arial", 12, "bold"),
            'normal': ("Arial", 12),
            'burn_button': ("Arial", 10, "bold"),
            'game_button': ("Arial", 8, "bold"),
            'stats': ("Courier", 9),
            'stats_window': ("Courier", 10)
        }

        # Tailles boutons
        self.button_sizes = {
            'burn_width': 8,
            'burn_height': 1,
            'game_width': 12,
            'game_height': 2,
            'game_bd': 2,
            'control_width': 12,
            'control_height': 1
        }

        # Espacements
        self.paddings = {
            'main_frame': "10",
            'section': "10",
            'section_y': (0, 10),
            'burn_buttons': (20, 0),
            'burn_button': 5,
            'game_button': 2,
            'control_button': 5,
            'stats_window': 10
        }

        # Dimensions widgets
        self.widget_sizes = {
            'stats_height': 6,
            'stats_width': 80,
            'stats_window_width': 800,
            'stats_window_height': 600,
            'grid_columns': 3
        }

        # ====================================================================
        # TEXTES ET MESSAGES
        # ====================================================================

        # Titres sections
        self.section_titles = {
            'burn': "🔥 Initialisation",
            'status': "📊 Statut de la Partie",
            'buttons': "🎲 Saisie Manches (Résultat + Nombre de cartes)",
            'stats': "📈 Statistiques Temps Réel",
            'stats_window': "📊 Statistiques Détaillées BCT"
        }

        # Labels
        self.labels = {
            'burn': "Brûlage:",
            'manche': "Manche : ",
            'window_title': "🧠 BCT - Baccarat Counting Tool"
        }

        # Messages
        self.messages = {
            'burn_already_init': "Brûlage déjà initialisé pour cette partie",
            'burn_first': "Veuillez d'abord initialiser le brûlage",
            'game_complete': "Partie complète: {} manches P/B atteintes",
            'no_game': "Aucune partie en cours",
            'new_game_confirm': "Voulez-vous vraiment démarrer une nouvelle partie ?\nLa partie actuelle sera perdue si non sauvegardée.",
            'quit_confirm': "Voulez-vous vraiment quitter ?\nLa partie actuelle sera perdue si non sauvegardée.",
            'invalid_cards': "Nombre de cartes invalide: {}",
            'game_saved': "Partie sauvegardée: {}",
            'new_game_init': "🆕 Nouvelle partie initialisée\nVeuillez initialiser le brûlage pour commencer\n",
            'reset_confirm': "Voulez-vous vraiment réinitialiser la partie ?\nToutes les données actuelles seront perdues (y compris le brûlage).",
            'reset_success': "🔄 Partie réinitialisée\nTous les compteurs remis à zéro\nVeuillez initialiser le brûlage pour recommencer\n",
            'undo_confirm': "Voulez-vous vraiment annuler la dernière main ?\nCette action supprimera la main {} et restaurera l'état précédent.",
            'undo_success': "↩️ Main {} annulée\nÉtat restauré à la main précédente\n",
            'undo_no_hand': "Aucune main à annuler.\nSeul le brûlage est présent.",
            'undo_only_burn': "Impossible d'annuler le brûlage.\nUtilisez 'Réinitialiser' pour recommencer."
        }

        # Boutons
        self.button_texts = {
            'pair': "PAIR",
            'impair': "IMPAIR",
            'save': "💾 Sauvegarder",
            'new_game': "🔄 Nouvelle Partie",
            'statistics': "📊 Statistiques",
            'quit': "❌ Quitter",
            'reset_game': "🔄 Réinitialiser",
            'undo_hand': "↩️ Annuler Main"
        }

        # ====================================================================
        # FICHIERS ET LOGGING
        # ====================================================================

        # Fichiers
        self.files = {
            'log': 'bct.log',
            'save_prefix': 'bct_game_',
            'save_extension': '.json',
            'encoding': 'utf-8'
        }

        # Formats
        self.formats = {
            'datetime_file': '%Y%m%d_%H%M%S',
            'json_indent': 2,
            'stats_separator': "=" * 60,
            'sequence_display_limit': 10,
            'recent_hands_limit': 5
        }

        # ====================================================================
        # MAPPING AUTOMATIQUE
        # ====================================================================

        # Mapping nombre de cartes → catégorie
        self.card_mapping = {
            4: 'A',  # pair_4 = A
            5: 'C',  # impair_5 = C
            6: 'B'   # pair_6 = B
        }

        # Descriptions cartes
        self.card_descriptions = {
            4: "4 cartes totales",
            5: "5 cartes totales",
            6: "6 cartes totales"
        }

        # Valeurs par défaut
        self.defaults = {
            'game_stats': "Partie: 0/60",
            'manche_display': "0 / 60",
            'empty_stats': " | TIE: 0",
            'default_sync': 0,  # SYNC
            'empty_string': '',
            'no_result': 'Aucune',
            'na_display': 'N/A'
        }